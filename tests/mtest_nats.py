from datetime import datetime, timezone, timedelta
from pprint import pprint as pp
import pandas as pd
import tradertools as tt


nts = tt.clients.natsio.NatsClient()

user_id = 'testuser'
name2 = 'ZNM25'


nts.publish('subscription.requests', {
    'action': 'add',
    'user_id': user_id,
    'name2': name2,
    'timestamp': (datetime.now(timezone.utc)-timedelta(seconds=24*60*60-1400)).isoformat()
})


nts.publish('subscription.requests', {
    'action': 'remove',
    'user_id': user_id,
    'name2': name2,
    'timestamp': datetime.now(timezone.utc).isoformat()
})


pp(nts.get_bucket('user_subscriptions'))



# historical 
x = nts.request('historical.requests', {
    'request_type': 'bars_in_period',
    'ticker': 'AAPL',
    'interval_len': 60*60,  # 60 minute bars
    'interval_type': 's',  # seconds
    'bgn_prd': (datetime.now(timezone.utc) - timedelta(days=100)).isoformat(),
    'end_prd': (datetime.now(timezone.utc) - timedelta(days=0)).isoformat(),
})



d = nts.get_object(x['store'], x['key']) #type: ignore

df = pd.DataFrame(d[1:], columns=d[0]) #type: ignore
df.set_index('timestamp', inplace=True)
print(df)