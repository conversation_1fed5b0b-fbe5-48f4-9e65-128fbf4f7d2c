
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
import pandas as pd

import tradertools as tt

### Historical Data

# only IQ tickers work at the moment this will change to name2 (see example below)
ticker = "NQM25" 

# datetime objects must contain a timezone
now = datetime.now(ZoneInfo('localtime'))
end_prd = datetime(now.year, now.month, now.day, now.hour, tzinfo=ZoneInfo("localtime"))
bgn_prd = end_prd - timedelta(days=7)

# query
bars = tt.marketdata.historical.get_bars_in_period(ticker, 30*60, bgn_prd, end_prd)

# dataframe - first row returned is the column headers
# df = pd.DataFrame(bars[1:], columns=bars[0])
df = pd.DataFrame(bars)
df.set_index('timestamp', inplace=True)
print(df.head(20))


### Live Data

# Get a single live quote (must have been subscribed)
name2 = "ESM25" # name2 is our internal naming scheme (Product + month code + 2 digit year)
print(tt.marketdata.live.get_quote(name2))




# Subscribe to live quotes and print them using the function
def printmsg(msg):
    print(tt.marketdata.live.Quote(*msg))
    
    
tt.marketdata.live.subscribe(name2, printmsg)


tt.marketdata.list_subscriptions()