import time
from datetime import date
import pandas as pd
import tradertools as tt



product = 'ZN'
step = 1


cur= date(2025, 1, 1)
end  = date.today()

products = tt.instruments.get_products()
classes = tt.instruments.get_instrument_classes()
defs = tt.instruments.get_definitions()
inst = tt.instruments.get_definition(product, step, end)
iqdefs = tt.instruments.get_iqfeed_definitions()


stime = time.time()
s = tt.instruments.generate_tradeDate_series(product, step, cur, end)
etime = time.time()
print(f"Time taken: {etime - stime}")

df = pd.DataFrame(s)

df.iloc[-1]

for x in s:
    print(x.tradeDate, x.name)
