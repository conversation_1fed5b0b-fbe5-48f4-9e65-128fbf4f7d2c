__all__ = ["setup_logging", "setup_child_process_logging", "get_log_queue", "setup_child_process_logging_with_queue"]

import os
import logging
import logging.handlers
import multiprocessing
from datetime import datetime
from typing import Optional

# Global variables to store queue and listener
_log_queue: Optional[multiprocessing.Queue] = None
_queue_listener: Optional[logging.handlers.QueueListener] = None

def setup_logging(title: str = "default", 
                 log_filename: str = 'output.log', 
                 append: bool = False, 
                 log_level=logging.INFO) -> logging.Logger:
    """
    Configure the root logger with both file and console handlers using a 
    QueueHandler for multiprocess safety.
    
    Sets up logging to both a file and console with a consistent format.
    In production environment (ENV='PRODUCTION'), the log filename will include
    the TITLE environment variable and current timestamp.
    
    Log files are stored in a 'logs' directory.
    
    Args:
        title (str, optional): Title to use in log filename. Defaults to 'default'.
        log_filename (str, optional): Name of the log file to write to.
            Defaults to 'output.log'.
        append (bool, optional): Whether to append to existing log file.
            Defaults to False.
        log_level (int, optional): Logging level to use (e.g. logging.INFO,
            logging.DEBUG). Defaults to logging.INFO.
            
    Returns:
        logging.Logger: Configured root logger instance.
        
    Note:
        Creates a 'logs' directory if it doesn't exist.
        Log format: [timestamp] [logger_name] [level] message
        This function should be called once in the main process before spawning
        any child processes.
    """
    global _log_queue, _queue_listener
    
    # If already setup, return existing logger
    if _log_queue is not None and _queue_listener is not None:
        return logging.getLogger()
    
    # If in production env, rename file to title and datetime
    env = os.getenv("ENV", "DEVELOPMENT")
    if env == "PRODUCTION":
        title = os.getenv("TITLE", title)
        dt = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        log_filename = f"{title}_{dt}.log"
    
    # Create logs directory if it doesn't exist
    logs_dir = './logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    log_path = os.path.join(logs_dir, log_filename)
    
    # Create the queue for multiprocess logging
    _log_queue = multiprocessing.Queue()
    
    # Create handlers for the listener
    mode = 'a' if append else 'w'
    file_handler = logging.FileHandler(log_path, mode=mode)
    console_handler = logging.StreamHandler()
    
    # Set formatter
    formatter = logging.Formatter('[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Create and start the listener
    _queue_listener = logging.handlers.QueueListener(
        _log_queue,
        file_handler,
        console_handler,
        respect_handler_level=True
    )
    _queue_listener.start()
    
    # Configure root logger to use QueueHandler
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.setLevel(log_level)
    
    # Add queue handler to root logger
    queue_handler = logging.handlers.QueueHandler(_log_queue)
    root_logger.addHandler(queue_handler)
    
    print(f"Multiprocess logging setup complete. Log file: {log_path}")
    
    # Register cleanup function
    import atexit
    atexit.register(_cleanup_logging)
    
    return root_logger


def get_log_queue() -> Optional[multiprocessing.Queue]:
    """
    Get the current log queue for passing to child processes.
    
    This function should be called in the main process to get the queue
    that can then be passed to child processes.
    
    Returns:
        multiprocessing.Queue if setup_logging() has been called, None otherwise.
        
    Example:
        In main process:
        ```python
        logger = setup_logging()
        queue = get_log_queue()
        
        # Pass queue to worker functions
        results = Parallel(n_jobs=4)(
            delayed(worker_function)(data, queue) for data in data_list
        )
        ```
    """
    global _log_queue
    return _log_queue


def get_queue_handler() -> Optional[logging.handlers.QueueHandler]:
    """
    Get a QueueHandler connected to the logging queue.
    
    This function should be called in child processes to get a handler
    that sends log records to the main process's queue.
    
    Returns:
        QueueHandler if the queue exists, None otherwise.
        
    Note:
        This function may return None in child processes due to multiprocessing
        memory isolation. Use setup_child_process_logging_with_queue() instead
        for better reliability in child processes.
    """
    global _log_queue
    if _log_queue is not None:
        return logging.handlers.QueueHandler(_log_queue)
    return None


def setup_child_process_logging_with_queue(log_queue: Optional[multiprocessing.Queue], 
                                         log_level=logging.INFO):
    """
    Setup logging in a child process with explicit queue parameter.
    
    This is the recommended way to setup logging in child processes when using
    joblib or other multiprocessing libraries.
    
    Args:
        log_queue: The multiprocessing.Queue from the parent process (get with get_log_queue())
        log_level: The logging level to use in the child process
        
    Example:
        In worker function:
        ```python
        def worker_function(data, log_queue):
            setup_child_process_logging_with_queue(log_queue)
            log = logging.getLogger(__name__)
            log.info("This will be logged from child process")
            # Your work here
        
        # In main process
        queue = get_log_queue()
        results = Parallel(n_jobs=4)(
            delayed(worker_function)(data, queue) for data in data_list
        )
        ```
    """
    if log_queue:
        handler = logging.handlers.QueueHandler(log_queue)
        logger = logging.getLogger()
        # Only clear and reconfigure if not already set up with a queue handler
        if not any(isinstance(h, logging.handlers.QueueHandler) for h in logger.handlers):
            logger.handlers.clear()
            logger.addHandler(handler)
            logger.setLevel(log_level)
            
            # Clear all existing loggers to ensure they inherit from root
            logging.Logger.manager.loggerDict.clear()
    else:
        # Fallback: if queue handler is not available, set up basic logging
        logging.basicConfig(
            level=log_level,
            format='[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s',
        )


def _cleanup_logging():
    """Clean up the queue listener when the program exits."""
    global _queue_listener
    if _queue_listener is not None:
        _queue_listener.stop()


# Legacy function for backward compatibility
def setup_child_process_logging(log_level=logging.INFO):
    """
    Convenience function to setup logging in a child process.
    
    WARNING: This function may not work reliably in child processes due to
    multiprocessing memory isolation. Use setup_child_process_logging_with_queue()
    instead for better reliability.
    
    Args:
        log_level: The logging level to use in the child process
    """
    handler = get_queue_handler()
    if handler:
        logger = logging.getLogger()
        # Only clear and reconfigure if not already set up
        if not any(isinstance(h, logging.handlers.QueueHandler) for h in logger.handlers):
            logger.handlers.clear()
            logger.addHandler(handler)
            logger.setLevel(log_level)
            
            # Clear all existing loggers to ensure they inherit from root
            logging.Logger.manager.loggerDict.clear()
    else:
        # Fallback: if queue handler is not available, set up basic logging
        logging.basicConfig(
            level=log_level,
            format='[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s',
        )

# __all__ = ["setup_logging", "setup_child_process_logging"]

# import os
# import logging
# import logging.handlers
# import multiprocessing
# from datetime import datetime
# from typing import Optional

# # Global variables to store queue and listener
# _log_queue: Optional[multiprocessing.Queue] = None
# _queue_listener: Optional[logging.handlers.QueueListener] = None

# def setup_logging(title: str = "default", 
#                  log_filename: str = 'output.log', 
#                  append: bool = False, 
#                  log_level=logging.INFO) -> logging.Logger:
#     """
#     Configure the root logger with both file and console handlers using a 
#     QueueHandler for multiprocess safety.
    
#     Sets up logging to both a file and console with a consistent format.
#     In production environment (ENV='PRODUCTION'), the log filename will include
#     the TITLE environment variable and current timestamp.
    
#     Log files are stored in a 'logs' directory.
    
#     Args:
#         title (str, optional): Title to use in log filename. Defaults to 'default'.
#         log_filename (str, optional): Name of the log file to write to.
#             Defaults to 'output.log'.
#         append (bool, optional): Whether to append to existing log file.
#             Defaults to False.
#         log_level (int, optional): Logging level to use (e.g. logging.INFO,
#             logging.DEBUG). Defaults to logging.INFO.
            
#     Returns:
#         logging.Logger: Configured root logger instance.
        
#     Note:
#         Creates a 'logs' directory if it doesn't exist.
#         Log format: [timestamp] [logger_name] [level] message
#         This function should be called once in the main process before spawning
#         any child processes.
#     """
#     global _log_queue, _queue_listener
    
#     # If already setup, return existing logger
#     if _log_queue is not None and _queue_listener is not None:
#         return logging.getLogger()
    
#     # If in production env, rename file to title and datetime
#     env = os.getenv("ENV", "DEVELOPMENT")
#     if env == "PRODUCTION":
#         title = os.getenv("TITLE", title)
#         dt = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
#         log_filename = f"{title}_{dt}.log"
    
#     # Create logs directory if it doesn't exist
#     logs_dir = './logs'
#     if not os.path.exists(logs_dir):
#         os.makedirs(logs_dir)
    
#     log_path = os.path.join(logs_dir, log_filename)
    
#     # Create the queue for multiprocess logging
#     _log_queue = multiprocessing.Queue()
    
#     # Create handlers for the listener
#     mode = 'a' if append else 'w'
#     file_handler = logging.FileHandler(log_path, mode=mode)
#     console_handler = logging.StreamHandler()
    
#     # Set formatter
#     formatter = logging.Formatter('[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s')
#     file_handler.setFormatter(formatter)
#     console_handler.setFormatter(formatter)
    
#     # Create and start the listener
#     _queue_listener = logging.handlers.QueueListener(
#         _log_queue,
#         file_handler,
#         console_handler,
#         respect_handler_level=True
#     )
#     _queue_listener.start()
    
#     # Configure root logger to use QueueHandler
#     root_logger = logging.getLogger()
#     root_logger.handlers.clear()
#     root_logger.setLevel(log_level)
    
#     # Add queue handler to root logger
#     queue_handler = logging.handlers.QueueHandler(_log_queue)
#     root_logger.addHandler(queue_handler)
    
#     print(f"Multiprocess logging setup complete. Log file: {log_path}")
    
#     # Register cleanup function
#     import atexit
#     atexit.register(_cleanup_logging)
    
#     return root_logger


# def get_queue_handler() -> Optional[logging.handlers.QueueHandler]:
#     """
#     Get a QueueHandler connected to the logging queue.
    
#     This function should be called in child processes to get a handler
#     that sends log records to the main process's queue.
    
#     Returns:
#         QueueHandler if the queue exists, None otherwise.
        
#     Example:
#         In a child process:
#         ```python
#         import logging
#         from your_module import get_queue_handler
        
#         # Configure logging in child process
#         handler = get_queue_handler()
#         if handler:
#             logger = logging.getLogger()
#             logger.handlers.clear()
#             logger.addHandler(handler)
#             logger.setLevel(logging.INFO)
        
#         # Now you can use logging normally
#         log = logging.getLogger(__name__)
#         log.info("This will be logged from child process")
#         ```
#     """
#     global _log_queue
#     if _log_queue is not None:
#         return logging.handlers.QueueHandler(_log_queue)
#     return None


# def _cleanup_logging():
#     """Clean up the queue listener when the program exits."""
#     global _queue_listener
#     if _queue_listener is not None:
#         _queue_listener.stop()


# # Example usage for child processes
# def setup_child_process_logging(log_level=logging.INFO):
#     """
#     Convenience function to setup logging in a child process.
    
#     Call this at the beginning of any function that will run in a child process.
    
#     Args:
#         log_level: The logging level to use in the child process
#     """
#     handler = get_queue_handler()
#     if handler:
#         logger = logging.getLogger()
#         # Only clear and reconfigure if not already set up
#         if not any(isinstance(h, logging.handlers.QueueHandler) for h in logger.handlers):
#             logger.handlers.clear()
#             logger.addHandler(handler)
#             logger.setLevel(log_level)
            
#             # Clear all existing loggers to ensure they inherit from root
#             logging.Logger.manager.loggerDict.clear()
#     else:
#         # Fallback: if queue handler is not available, set up basic logging
#         logging.basicConfig(
#             level=log_level,
#             format='[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s',
#         )