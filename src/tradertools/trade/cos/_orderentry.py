
__all__ = ["placeStockSafetyOrders", "cancelStockStop", "cancelAllStockStops"]

from typing import Optional
from ib_async import Stock, StopLimitOrder, LimitOrder, Trade

from tradertools.clients import get_ib_client

# _default_client = "ib_paper"
# current_client = _default_client


ORDERREF = "cos"
SAFETYSTOPS = "safetystops"




def placeStockSafetyOrders(name: str, side: str, qty: int, stopPrice: float, limitDistance: float, pukeDistance: Optional[float]=None, profitDistance: Optional[float]=None, client_name: str = 'ib_paper') -> list[Trade]:
    """
    Place a parent stop order and a child stop order for a stock.

    Args:
        name (str): ib ticker symbol
        side (str): BUY or SELL
        qty (int): quantity to trade
        stopPrice (float): Trigger price to enter limit
        limitDistance (float): Distance from limitPrice to enter stop order.  Side determines if this is above or below limitPrice.
        pukeDistance (float): Stop order distance from parent stop price.  Side determines if this is above or below parent stop price.
        client (Optional[str], optional): Client to use. Defaults to 'ib_paper'.

    Returns:
        list[Trade]: List of ib_async trade objects
        
    Example:
        name = "AAPL"
        side = "BUY"
        qty = 100

        
        
        
    """
    if side not in ['BUY', 'SELL']:
        raise ValueError(f"Invalid side: {side}")
    
    if qty <= 0 or qty > 6000:
        raise ValueError(f"Invalid quantity: {qty}")
    
    ib = get_ib_client(client_name)
    
    trades = []
    
    contract = Stock(name, "SMART", "USD")
    ib.ib.qualifyContracts(contract)
    
    orderref = f"{ORDERREF}.{name}.safetystops"
    
    if side == 'BUY':
        limitPrice = stopPrice + limitDistance
        child_side = 'SELL'
        pukeStopPrice = stopPrice - pukeDistance if pukeDistance else None
        pukeLimitPrice = limitPrice - pukeDistance if pukeDistance else None
        profitLimitPrice = limitPrice + profitDistance if profitDistance else None
        
    else:
        limitPrice = stopPrice - limitDistance
        child_side = 'BUY'
        pukeStopPrice = stopPrice + pukeDistance if pukeDistance else None
        pukeLimitPrice = limitPrice + pukeDistance if pukeDistance else None
        profitLimitPrice = limitPrice - profitDistance if profitDistance else None
        
        
    parent = StopLimitOrder(side, qty, limitPrice, stopPrice)
    parent.orderId = ib.ib.client.getReqId()
    parent.tif = "GTC"
    parent.outsideRth = True
    parent.orderRef = orderref
    parent.transmit = False
    
    trades.append(ib.ib.placeOrder(contract, parent))
    print(f"Parent order: {parent}")
    
    if pukeStopPrice is not None:
        ib.ib.sleep(.1)
        puke = StopLimitOrder(child_side, qty, pukeLimitPrice, pukeStopPrice) # type: ignore
        puke.orderId = ib.ib.client.getReqId()
        puke.parentId = parent.orderId
        puke.tif = "GTC"
        puke.outsideRth = True
        puke.orderRef = orderref
        puke.transmit = True
        trades.append(ib.ib.placeOrder(contract, puke))
        print(f"Puke order: {puke}")
    
    if profitLimitPrice is not None:
        ib.ib.sleep(.1)
        profit = LimitOrder(child_side, qty, profitLimitPrice)
        profit.orderId = ib.ib.client.getReqId()
        profit.parentId = parent.orderId
        profit.tif = "GTC"
        profit.outsideRth = True
        profit.orderRef = orderref
        profit.transmit = True
        trades.append(ib.ib.placeOrder(contract, profit))
        print(f"Profit order: {profit}")
    
    return trades



def cancelStockStop(name: str, client_name: str = 'ib_paper'):
    
    ib = get_ib_client(client_name)
    
    trades = ib.ib.reqAllOpenOrders()
    
    for t in trades:
        orderref = t.order.orderRef
        if ORDERREF in orderref and name in orderref and SAFETYSTOPS in orderref:
            print(f"Cancelling order: {t.order}")
            ib.ib.cancelOrder(t.order)
            ib.ib.sleep(.1)
    
    
def cancelAllStockStops(client_name: str = 'ib_paper'):
    ib = get_ib_client(client_name)
    
    trades = ib.ib.reqAllOpenOrders()
    
    for t in trades:
        orderref = t.order.orderRef
        if ORDERREF in orderref and SAFETYSTOPS in orderref:
            print(f"Cancelling order: {t.order}")
            ib.ib.cancelOrder(t.order)
            ib.ib.sleep(.1)
            


# def setClient(client: str):
#     global current_client
#     print(f"SETTING CLIENT TO {client}")
#     current_client = client
