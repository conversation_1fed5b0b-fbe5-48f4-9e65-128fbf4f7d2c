__all__ = ["InstrumentClass"]

from dataclasses import dataclass
from typing import Dict, List, Any
import orjson

@dataclass
class InstrumentClass:
    product: str
    exchange: str
    broker: str
    data_provider: str
    contract_months: List[str]
    tradeable_months: List[str]
    num_roll_days: int
    num_subscribable: int
    num_recordable: int
    meta: Dict[str, Any]
    tags: List[str]
    ignore: bool
    broker_data: Dict[str, Any]
    data_provider_data: Dict[str, Any]
    
    def __post_init__(self):
        # Handle JSON fields that might come as strings
        if isinstance(self.meta, str):
            self.meta = orjson.loads(self.meta)
        
        if isinstance(self.tags, str):
            self.tags = orjson.loads(self.tags)
            
        if isinstance(self.broker_data, str):
            self.broker_data = orjson.loads(self.broker_data)
            
        if isinstance(self.data_provider_data, str):
            self.data_provider_data = orjson.loads(self.data_provider_data)