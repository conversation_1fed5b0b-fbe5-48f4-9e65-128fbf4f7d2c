__all__ = ['Instrument']
from dataclasses import dataclass
from datetime import date
from decimal import Decimal

@dataclass(slots=True)
class Instrument:
    """Dataclass for financial instrument information from the definitions view."""
    
    # product contract identifiers and naming
    product: str = None # type: ignore
    name: str = None # type: ignore
    name2: str = None # type: ignore
    longName: str = None # type: ignore
   
    # Date reference parameter
    tradeDate: date = None # type: ignore
    stepName: str = None # type: ignore
    absName: str = None # type: ignore
   
    # Exchange and classification information
    exchange: str = None # type: ignore
    currency: str = None # type: ignore
    secType: str = None # type: ignore
   
    # Pricing information
    minTick: Decimal = None # type: ignore
    pointValue: Decimal = None # type: ignore
    tickValue: Decimal = None # type: ignore
    multiplier: Decimal = None # type: ignore
    priceMagnifier: int = None # type: ignore
   
    # Date and expiration information
    expiry: date = None # type: ignore
    rollDate: date = None # type: ignore
    year: int = None # type: ignore
    month: int = None # type: ignore
    monthCode: str = None # type: ignore
    contractMonth: date = None # type: ignore
   
    broker: str = None # type: ignore
    conId: int = None # type: ignore
    
    data_provider: str = None # type: ignore
    dp_symbol: str = None # type: ignore
    
    # Additional metadata 
    meta: dict = None # type: ignore 
    synthetic: bool = False  # Flag indicating if this is a synthesized contract
   
    @classmethod
    def from_dict(cls, data: dict) -> 'Instrument':
        """
        Create an Instrument instance from a dictionary.
       
        Args:
            data: Dictionary containing instrument data
           
        Returns:
            Instrument instance
        """
        # Handle date fields if they're strings
        for date_field in ['tradeDate', 'expiry', 'rollDate', 'contractMonth']:
            if isinstance(data.get(date_field), str):
                try:
                    parts = data[date_field].split('-')
                    if len(parts) == 3:
                        data[date_field] = date(int(parts[0]), int(parts[1]), int(parts[2]))
                except (ValueError, IndexError):
                    data[date_field] = None
        
        # Handle numeric fields - ensure they're Decimal objects
        numeric_fields = ['minTick', 'pointValue', 'tickValue', 'multiplier']
        for field in numeric_fields:
            if field in data and data[field] is not None and not isinstance(data[field], Decimal):
                try:
                    data[field] = Decimal(str(data[field]))
                except (ValueError, TypeError):
                    pass
       
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

    def to_dict(self) -> dict:
        """
        Convert the Instrument instance to a dictionary.
       
        Returns:
            Dictionary representation of the instrument
        """
        result = {}
        for k, v in self.__dict__.items():
            if v is not None:
                if isinstance(v, date):
                    result[k] = v.isoformat()
                else:
                    result[k] = v
        return result