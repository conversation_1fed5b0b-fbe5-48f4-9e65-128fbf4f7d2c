__all__ = ['IQFeedDefinition']

from dataclasses import dataclass
from tradertools.instruments._instrument import Instrument


@dataclass
class IQFeedDefinition:
    product: str
    symbol: str
    
    def parse_symbol(self, instrument: Instrument) -> str:
        """Parse the IQFeed symbol from the instrument definition."""
        if instrument.secType == 'FUT':
            return f"{self.product}{instrument.monthCode}{instrument.year[-2:]}"
        else:
            return self.symbol
    
