__all__ = ['Product']



from dataclasses import dataclass
from datetime import date, timedelta
from typing import Dict, List, Tuple, Any, Optional

import orjson

from tradertools.enums import MonthCode


@dataclass
class Product:
    product: str
    exchange: str
    broker: str
    data_provider: str
    contract_months: List[str]
    tradeable_months: List[str]
    num_roll_days: int
    num_subscribable: int
    num_recordable: int
    meta: Dict[str, Any]
    tags: List[str]
    ignore: bool
    
    def __post_init__(self):
        # Handle JSON fields that might come as strings
        if isinstance(self.meta, str):
            self.meta = orjson.loads(self.meta)
        
        if isinstance(self.tags, str):
            self.tags = orjson.loads(self.tags)
            
            
    def get_step_name2(self, step: int, tradeDate: Optional[date]=None, num_roll_days: Optional[int]=None) -> str:
        """
        Get the name2 (contract identifier) for a given step and trade date.
        
        Args:
            step (int): Contract step (1=front month, 2=second month, etc.)
            tradeDate (Optional[date], optional): Date for which to get the contract. Defaults to today.
            num_roll_days (Optional[int], optional): Days to add for roll calculation. Defaults to db value.
        
        Returns:
            str: name2 of the contract
        """
        
        if tradeDate is None:
            tradeDate = date.today()
        
        if num_roll_days is None:
            num_roll_days = self.num_roll_days
    
        # Use the shared roll schedule calculation
        # Calculate from a year before to a year after to ensure we find the contract
        calc_start = date(tradeDate.year - 1, 1, 1)
        calc_end = date(tradeDate.year + 1, 12, 31)
        
        roll_schedule = self.get_roll_schedule(step, calc_start, calc_end)
        
        # Find the active contract for the given trade date
        active_contract = None
        for i, (roll_date, contract_code) in enumerate(roll_schedule):
            if roll_date > tradeDate:
                # The previous contract is active, or this is the first one
                if i > 0:
                    active_contract = roll_schedule[i-1][1]
                else:
                    active_contract = contract_code
                break
        else:
            # If we didn't break, use the last contract
            if roll_schedule:
                active_contract = roll_schedule[-1][1]
        
        if active_contract is None:
            raise ValueError(f"Could not determine contract for date {tradeDate}")
        
        return active_contract
                

    def get_tradeDate_contract_series(self, step: int, start_date: date, end_date: date, num_roll_days: Optional[int]=None, include_weekend: bool=False) -> List[Dict[str, Any]]: 
        """
        Creates a list of dates and the corresponding step contract.  Defaults to values in instruments.products table

        Args:
            step (int): Contract step (1=front month, 2=second month, etc.)
            start_date (date): Start date (inclusive)
            end_date (date): End date (inclusive)
            num_roll_days (Optional[int], optional): Days to add for roll calculation. Defaults to db value.
            include_weekend (bool, optional): Whether to include weekend dates in the series. Defaults to False.

        Raises:
            ValueError: If no roll schedule is calculated

        Returns:
            List[Dict[str, Any]]: List of dates and corresponding contracts
                - tradeDate: date
                - name2: contract name
        """
        
        if num_roll_days is None:
            num_roll_days = self.num_roll_days
        
        
        series = []
        roll_schedule = self.get_roll_schedule(step, start_date, end_date)
        
        current_date = start_date
        roll_index = 0
        current_contract = roll_schedule[0][1] if roll_schedule else None
        
        if current_contract is None:
            raise ValueError("No roll schedule calculated")
        
        while current_date <= end_date:
            # Skip weekends if not included
            if not include_weekend and current_date.weekday() >= 5:
                current_date += timedelta(days=1)
                continue
            
            # Check if we need to roll to next contract
            if (roll_index < len(roll_schedule) and 
                current_date >= roll_schedule[roll_index][0]):
                roll_index += 1
                current_contract = roll_schedule[roll_index][1]
            
            # yield (current_date, self.product + current_contract)
            series.append({
                'tradeDate': current_date, 
                'name2': self.product + current_contract
            })
            current_date += timedelta(days=1)
            
        return series
    
    
    def get_roll_schedule(self, step: int, start_date: date, end_date: date) -> List[Tuple[date, str]]:
        """
        Calculate the roll schedule for a given step within a date range.
        
        Args:
            step (int): Contract step (1=front month, 2=second month, etc.)
            start_date (date): Start date for the schedule
            end_date (date): End date for the schedule
        
        Returns:
            List[Tuple[date, str]]: List of roll dates and corresponding contract name2's
        """
        roll_schedule = []
        
        # Start from a bit before start_date to ensure we catch the active contract
        # calc_start = date(start_date.year - 1, 1, 1)
        calc_start = start_date
        current_year = calc_start.year
        month_index = 0
        steps_found = 0
        
        while True:
            tm = self.tradeable_months[month_index]
            roll_date = date(current_year, MonthCode.str2int(tm), 1) + timedelta(days=self.num_roll_days)
            
            if roll_date >= calc_start:
                steps_found += 1
                if steps_found == step:
                    contract_code = f"{tm}{current_year % 100}"
                    roll_schedule.append((roll_date, self.product + contract_code))
                    steps_found = 0  # Reset to find next contract of same step
                    
                    # Stop if we're well past end_date
                    if roll_date > end_date + timedelta(days=365):
                        break
            
            # Move to next month
            month_index = (month_index + 1) % len(self.tradeable_months)
            if month_index == 0:
                current_year += 1
        
        return sorted(roll_schedule)