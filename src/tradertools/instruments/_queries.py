"""Queries for instrument data."""
__all__ = [
    "get_products",
    "get_product",
    # "get_instrument_classes", 
    "get_definitions", 
    "get_definition",
    # "get_definition_by_step",
    "get_iqfeed_definitions", "get_iqfeed_definition",
]

from functools import cache
from typing import List, Tuple, Any, Optional
from datetime import date

from tradertools.clients import get_pg_client
from tradertools.instruments._product import Product
from tradertools.instruments._instrument import Instrument
from tradertools.instruments._iqfeed_definition import IQFeedDefinition

from . import default_connection



month_code_map = {
    'F': 1,
    'G': 2,
    'H': 3,
    'J': 4,
    'K': 5,
    'M': 6,
    'N': 7,
    'Q': 8,
    'U': 9,
    'V': 10,
    'X': 11,
    'Z': 12
}

products = list[Product]()  # This will be populated with product information from the database
iqfeed_definitions = list[IQFeedDefinition]()  # This will be populated with IQFeed definitions from the database

@cache
def get_products(force_refresh: bool = False) -> list[Product]:
    """Get product information from the database.
    
    Returns:
        list[dict]: List of product information dictionaries
    """
    global products
    
    if products and not force_refresh:
        return products
    
    query = "SELECT * FROM instruments.products"
    
    conn = get_pg_client(default_connection)
    result = conn.query(query)
    
    
    
    products = [Product(**row) for row in result]  # Store the result in the global variable
    # Convert each row to a dictionary
    return products


def get_product(product: str) -> Product:
    """Get product information from the database.
    
    Returns:
        dict: Product information dictionary
    """
    products = get_products()
    ret = [p for p in products if p.product == product]
    if ret:
        return ret[0]
    else:
        raise ValueError(f"Product '{product}' not found")


# def get_instrument_classes(**filters) -> list[InstrumentClass]:
#     """Get instrument classes from the database with optional filtering.
    
#     Any keyword argument will be used as a filter condition on the corresponding column.
    
#     Args:
#         **filters: Column-value pairs to filter results (e.g., broker='IB', exchange='CME')
    
#     Returns:
#         list[InstrumentClass]: List of filtered instrument classes
#     """
#     # Base query
#     query = "SELECT * FROM instruments.instrument_classes"
#     where_params = None
    
#     # Build WHERE clause from filters
#     if filters:
#         where_conditions = []
#         where_params = {}
        
#         for i, (column, value) in enumerate(filters.items()):
#             param_name = f"p{i}"
#             where_conditions.append(f"{column} = %({param_name})s")
#             where_params[param_name] = value
        
#         query += " WHERE " + " AND ".join(where_conditions)
    
#     # Execute parameterized query (prevents SQL injection)
#     conn = get_pg_client(default_connection)
#     result = conn.query(query, where_params if filters else None)
    
#     # Create and return InstrumentClass objects
#     return [InstrumentClass(**row) for row in result]


def get_definitions(include_expired: bool = True, include_synthetic: bool = True) -> list[Instrument]:
    """Get definitions of instrument classes from the database.
    
    Returns:
        list[dict]: List of dictionaries containing definitions of instrument classes
    """
    query = "SELECT * FROM instruments.definitions"
    where_params = []
    if not include_synthetic:
        where_params.append("NOT synthetic")
    if not include_expired:
        where_params.append("expiry >= CURRENT_DATE")
    if where_params:
        query += " WHERE " + " AND ".join(where_params)
    
    conn = get_pg_client(default_connection)
    result = conn.query(query)
    
    # Convert each row to a dictionary
    return [Instrument(**row) for row in result]



def get_definition(name2: Optional[str]=None, product: Optional[str]=None, step: int=1, tradeDate: Optional[date]=None) -> Instrument:
    """Get the definition of a specific instrument from the database.
    Args:
        name2: name2 of the instrument
        product: product symbol of the instrument
        step: Contract number
        tradeDate: Date for which to get the instrument definition (default is today)
    Returns:
        Instrument: Definition of the instrument
    """
    result: list[dict[str, Any]] = []
    
    if not name2 and not product:
        raise ValueError("Must specify either name2 or product")
    
    conn = get_pg_client(default_connection)
    if name2:
        query = f"SELECT * FROM instruments.definitions WHERE name2 = '{name2}'"
        result = conn.query(query)
    elif product:
        query = f"SELECT * FROM instruments.definitions WHERE product = '{product}'"
    
    
    if not result or len(result) == 0:
        raise ValueError(f"Unable to get definition for name2 '{name2}'")
    
    return Instrument(**result[0])
    


# def get_definition_by_step(product: str, step: int, tradeDate: Optional[date]=None) -> Instrument:
#     """Get the definition of a specific instrument from the database.
#     Args:
#         product: product symbol of the instrument
#         step: Contract number
#         tradeDate: Date for which to get the instrument definition (default is today)
#     Returns:
#         Instrument: Definition of the instrument
#     """
#     # Use today's date if tradeDate is not provided
#     products = get_products()
    
#     if tradeDate is None:
#         tradeDate = date.today()
        
#     # Get the contract from the database
#     query = "SELECT * FROM instruments.get_contract_by_step(%s, %s, %s)"
#     conn = get_pg_client(default_connection)
#     result = conn.query(query, (tradeDate, product, step))
    
#     if not result or len(result) == 0:
#         raise ValueError(f"Unexpected empty result for product '{product}' and step {step}")
    
#     # Convert to dictionary
#     db_contract = result[0]
    
#     # Get product info to check if we need to synthesize
#     product_info = next((r for r in products if r['product'] == product), None)
#     if not product_info or not product_info.get('tradeable_months'):
#         # Can't determine if synthesis is needed - just return the database result
#         return Instrument(**db_contract)
    
#     # Determine what the contract's month code and year should be
#     expected_month_code, expected_year = calc_calendar_code(
#         tradeDate, step, product_info['tradeable_months']
#     )
    
#     # If either the expected month code or expected year doesn't match what we got, synthesize
#     if (expected_month_code != db_contract.get('monthCode') or 
#         str(expected_year) != db_contract.get('year')):
#         # Synthesize the contract
#         synth_data = synthesize_contract(
#             db_contract, product_info, step, expected_month_code, expected_year, tradeDate
#         )
#         return Instrument(**synth_data)
    
#     # No synthesis needed - return the database result directly
#     return Instrument(**db_contract)



def get_iqfeed_definitions(force_refresh: bool = True) -> list[IQFeedDefinition]:
    """Get IQFeed definitions from the database.
    
    Returns:
        list[IQFeedDefinition]: List of IQFeed definitions
    """
    global iqfeed_definitions
    
    if iqfeed_definitions and not force_refresh:
        return iqfeed_definitions
    
    query = "SELECT * FROM instruments.products_iqfeed"
    conn = get_pg_client(default_connection)
    result = conn.query(query)
    
    # Convert each row to an IQFeedDefinition object
    iqfeed_definitions = [IQFeedDefinition(**row) for row in result]
    return iqfeed_definitions


def get_iqfeed_definition(product: str) -> IQFeedDefinition:
    """Get the IQFeed definition for a specific product.
    
    Args:
        product: Product symbol
    
    Returns:
        IQFeedDefinition: IQFeed definition for the product
    """
    definitions = get_iqfeed_definitions()
    return next((d for d in definitions if d.product == product), IQFeedDefinition(product, product))


def calc_calendar_code(trade_date: date, step: int, tradeable_months: List[str]) -> Tuple[str, int]:
    """Determine what the expected month and year code should be for a given date and step.
    
    Args:
        trade_date: The reference date
        step: Contract step (1=front month, 2=second month, etc.)
        tradeable_months: List of tradeable month codes
    
    Returns:
        tuple: The expected (month_code, year)
    """
    # Sort tradeable months by their position in the year
    sorted_months = sorted(tradeable_months, key=lambda m: month_code_map.get(m, 0))
    
    # Current month and year
    current_month = trade_date.month
    current_year = trade_date.year
    
    # For step 1 with a past date like 2018-01-01
    # The contract should be the next available one in the tradeable_months
    # For March (H) contract in 2018, this would be "ZWH8"
    
    # Find the next tradeable month after the current month
    next_month_code = None
    next_month_year = current_year
    
    # Check if any tradeable months are after the current month in the same year
    for month_code in sorted_months:
        month_num = month_code_map.get(month_code, 0)
        if month_num > current_month:
            next_month_code = month_code
            break
    
    # If no months are available in the current year, use the first month of next year
    if next_month_code is None:
        next_month_code = sorted_months[0]
        next_month_year = current_year + 1
    
    # For step 1, return the next available contract
    if step == 1:
        return (next_month_code, trade_date.year)  # Explicitly use trade_date.year
    
    # For other steps, need to find the correct contract in sequence
    if step > 1:
        # Count forward from the next available contract
        month_index = sorted_months.index(next_month_code)
        additional_steps = step - 1  # we already counted the first step
        
        while additional_steps > 0:
            month_index = (month_index + 1) % len(sorted_months)
            if month_index == 0:  # Wrapped around, increment year
                next_month_year += 1
            additional_steps -= 1
        
        return (sorted_months[month_index], next_month_year)
    else:  # step <= 0
        # For negative or zero steps, we need to go backward from the current position
        # This is more complex as we need to handle going back in time
        
        # Start with the previous month before the next available one
        if next_month_code == sorted_months[0]:
            # If the next month is the first in the sequence, go back to the last month of previous year
            prev_month_index = len(sorted_months) - 1
            prev_month_year = next_month_year - 1
        else:
            prev_month_index = sorted_months.index(next_month_code) - 1
            prev_month_year = next_month_year
        
        # Adjust for step (remember step is zero or negative)
        additional_steps = abs(step)
        
        while additional_steps > 0:
            prev_month_index = (prev_month_index - 1) % len(sorted_months)
            if prev_month_index == len(sorted_months) - 1:  # Wrapped around, decrement year
                prev_month_year -= 1
            additional_steps -= 1
        
        return (sorted_months[prev_month_index], prev_month_year)


