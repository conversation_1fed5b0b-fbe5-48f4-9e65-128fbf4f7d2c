__all__ = ['Bar']


from dataclasses import dataclass
from datetime import datetime
from zoneinfo import ZoneInfo


@dataclass
class Bar:
    timestamp: datetime | None
    name2: str | None
    open: float | None
    high: float | None
    low: float | None
    close: float | None
    total_volume: float | None
    period_volume: float | None
    num_trades: int | None
    
    def __post_init__(self):
        if isinstance(self.timestamp, str):
            self.timestamp = datetime.fromisoformat(self.timestamp).astimezone(ZoneInfo("UTC"))
        
    def __str__(self):
        return f"Bar(timestamp={self.timestamp}, name2={self.name2}, open={self.open}, high={self.high}, low={self.low}, close={self.close}, total_volume={self.total_volume}, period_volume={self.period_volume}, num_trades={self.num_trades})"
