__all__ = [
    "get_bars_in_period",
]

from datetime import datetime
from zoneinfo import ZoneInfo
from typing import Any

from tradertools.clients import get_nats_client
from tradertools.instruments import get_definition

from . import default_connection
from . import Bar


def get_bars_in_period(name2: str, interval_len: int, bgn_prd: datetime, end_prd: datetime, timeout: float = 10.0) -> Any:
    """Get bars for a specific ticker within a given period.
    
    Args:
        ticker: Ticker symbol
        interval_len: Length of each bar in seconds
        bgn_prd: Beginning of period (ISO 8601 format)
        end_prd: End of period (ISO 8601 format)
    
    Returns:
        list[dict]: List of bars
    """
    inst_def = get_definition(name2)
    
    ticker = inst_def.dp_symbol
    
    # ensure timzone info
    if bgn_prd.tzinfo is None:
        bgn_prd = bgn_prd.replace(tzinfo=ZoneInfo("localtime"))
    if end_prd.tzinfo is None:
        end_prd = end_prd.replace(tzinfo=ZoneInfo("localtime"))
        
    # round times to nearest second
    bgn_prd = bgn_prd.replace(microsecond=0)
    end_prd = end_prd.replace(microsecond=0)
    
    bars = list[Bar]()
    
    conn = get_nats_client(default_connection)
    response = conn.request('historical.requests', {
        'request_type': 'bars_in_period',
        'ticker': ticker,
        'interval_len': interval_len,
        'interval_type': 's',
        'bgn_prd': bgn_prd,
        'end_prd': end_prd
    }, timeout=timeout)
    
    if isinstance(response, dict):
        store = response['store']
        key = response['key']
        
        # obj should be a 2d list.  1st row is column names
        obj = conn.get_object(store, key)
        if obj:
            for row in obj[1:]:
                bar = Bar(
                    timestamp=row[0],
                    name2=name2,
                    open=row[1],
                    high=row[2],
                    low=row[3],
                    close=row[4],
                    total_volume=row[5],
                    period_volume=row[6],
                    num_trades=row[7]
                )
                bars.append(bar)
        
        return bars
    