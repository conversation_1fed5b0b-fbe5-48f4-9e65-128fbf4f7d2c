__all__ = ['get_quote', 'subscribe', 'unsubscribe', 'list_subscriptions']

from typing import Dict, List
import os
from datetime import datetime
from typing import Callable
from zoneinfo import ZoneInfo

from tradertools.clients import get_nats_client

from . import default_connection
from . import Quote




def get_quote(name2: str) -> Quote:
    """Get the last quote for a specific ticker.

    Args:
        name2 (str): name2 of the instrument

    Returns:
        Quote: The last quote for the ticker. If no quote is available, returns a Quote with all values set to None.
    """
    conn = get_nats_client(default_connection)
    result = conn.get_last_stream_value("MARKET_DATA", f"md.{name2}")
    if result:
        return Quote(name2, *result)
    else:
        return Quote(None, None, None, None, None, None, None, None)



def subscribe(name2: str, callback: Callable) -> None:
    """Subscribe to market data for a specific ticker.
    
    Args:
        name2 (str): name2 of the instrument
        callback (Callable): Function to call when new data arrives.
            The callback will receive a Quote object with the symbol field populated.
    """
    conn = get_nats_client(default_connection)
    
    user_id = os.getenv("USER") if os.getenv("TITLE") == 'tradertools' else os.getenv("TITLE")
    conn.publish("subscription.requests", {
        'action': 'add',
        'user_id': user_id,
        'name2': name2,
        'timestamp': datetime.now(ZoneInfo("UTC")).isoformat()
    })
    
    # Create a wrapper that converts the raw message to a Quote object with symbol
    def wrapper(msg):
        quote = Quote(name2, *msg)
        callback(quote)
    
    conn.subscribe(f"md.{name2}", wrapper)


def unsubscribe(name2: str, user_id: str | None = None) -> None:
    """Unsubscribe from market data for a specific ticker.
    
    Args:
        name2 (str): name2 of the instrument
        user_id (str): User ID to unsubscribe. If not provided, uses the current user.
    """
    if user_id is None:
        user = os.getenv("USER", "unknown")
        env = str(os.getenv("ENV"))
        title = str(os.getenv("TITLE"))
        user_id = user + '_' + title if title == 'tradertools' else env + '_' + title
    conn = get_nats_client(default_connection)
    conn.publish("subscription.requests", {
        'action': 'remove',
        'user_id': user_id,
        'name2': name2,
        'timestamp': datetime.now(ZoneInfo("UTC")).isoformat()
    })
    

    
def list_subscriptions() -> Dict[str, List[str]]:
    """List all active subscriptions for the current user.
    
    Returns:
        list[str]: List of subscribed tickers
    """
    conn = get_nats_client(default_connection)
    result = conn.get_bucket('user_subscriptions')
    return result
    