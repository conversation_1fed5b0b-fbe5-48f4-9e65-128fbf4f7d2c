__all__ = ['Quote']

from dataclasses import dataclass
from datetime import datetime
from zoneinfo import ZoneInfo


@dataclass
class Quote:
    name2: str | None
    timestamp: datetime | None
    bid: float | None
    ask: float | None
    bid_size: float | None
    ask_size: float | None
    last: float | None
    volume: float | None
    
    def __post_init__(self):
        if isinstance(self.timestamp, int):
            self.timestamp = datetime.fromtimestamp(self.timestamp/1_000_000, tz=ZoneInfo("UTC"))
            
    def __str__(self):
        ts = self.timestamp.astimezone(ZoneInfo('localtime')) if self.timestamp else None
        return f"Quote(name2={self.name2}, timestamp={ts}, bid={self.bid}, ask={self.ask}, bid_size={self.bid_size}, ask_size={self.ask_size}, last={self.last}, volume={self.volume})"
    