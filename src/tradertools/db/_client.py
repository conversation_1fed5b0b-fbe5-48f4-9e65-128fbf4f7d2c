__all__ = ["DatabaseClient"]

from abc import ABC, abstractmethod
# from typing import TypeVar

# T = TypeVar('T')





class DatabaseClient(ABC):
    """Base interface for all database clients."""
    
    @abstractmethod
    def connect(self, **kwargs) -> None:
        """Establish connection to the database."""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """Close connection to the database."""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """Check if connection is established."""
        pass
    
    # @property
    # @abstractmethod
    # def connection(self) -> T:
    #     """Return the underlying connection object."""
    #     pass
