"""Connection config classes for database configurations."""
__all__ = []
# __all__ = ["ConnectionConfig"]

from dataclasses import dataclass
from typing import Dict, Any, ClassVar, Type


@dataclass
class ConnectionConfig:
    """Base class for database connection config."""
    
    # Registry of connection detail classes by type
    _registry: ClassVar[Dict[str, Type["ConnectionConfig"]]] = {}
    
    # String identifier for this connection type
    type_id: ClassVar[str] = "base"
    
    @classmethod
    def register(cls, conn_cls: Type["ConnectionConfig"]) -> Type["ConnectionConfig"]:
        """Register a connection config class by its type_id."""
        cls._registry[conn_cls.type_id] = conn_cls
        return conn_cls
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConnectionConfig":
        """Create a connection config instance from a dictionary."""
        # Copy the dict to avoid modifying the original
        data_copy = data.copy()
        
        # Create instance with the provided data
        return cls(**data_copy)
    
    @classmethod
    def create(cls, db_type: str, config: Dict[str, Any]) -> "ConnectionConfig":
        """Create an appropriate connection config instance based on the type."""
        if db_type not in cls._registry:
            raise ValueError(f"1. Unsupported database type: {db_type}")
            
        conn_cls = cls._registry[db_type]
        return conn_cls.from_dict(config)
        
    def connection_args(self) -> Dict[str, Any]:
        """Get arguments for creating a database connection."""
        # Convert dataclass to dict, filtering out None values
        return {k: v for k, v in self.__dict__.items() if v is not None}

