# """Database connection manager for maintaining global database connections."""
# __all__ = [
#     'manager',
#     'current_env',
#     'pg',
#     'list_envs',
#     'list_configs',
#     'get_config',
#     'switch_env',
#     'switch_pg',
# ]

# from dotenv import load_dotenv
# load_dotenv()

# import os
# import yaml
# from typing import Dict, Any, Optional, ClassVar, Union, TypeVar
# import logging

# from deepmerge import always_merger
# from tradertools.db._connection_config import ConnectionConfig
# from tradertools.db._client import DatabaseClient
# from tradertools.db.postgres._client import PostgresClient
# from tradertools.db._db_types import DbType

# log = logging.getLogger(__name__)


# # Type for database clients
# T = TypeVar('T')


# DEFAULT_CONFIG = {
#     'connections': {
#         'postgres_local': {
#             'db': DbType.POSTGRES.value,
#             'host': 'localhost',
#             'port': 5432,
#             'user': 'postgres',
#             'password': '',
#             'database': 'postgres',
#             'minimum_pool_size': 1,
#             'maximum_pool_size': 5,
#         },
#         # 'redis_local': {
#         #     'db': DbType.REDIS.value,
#         #     'host': 'localhost',
#         #     'port': 6379,
#         #     'password': '',
#         # },  
#     },
#     'environments': {
#         'default': {
#             'pg': 'postgres_local',
#             # 'rmd': 'redis_local',
#         },
#     },
# }


# class DBManager:
#     """Singleton manager for database connections across different environments."""
    
#     _instance: ClassVar[Optional['DBManager']] = None
    
#     def __new__(cls, *args, **kwargs):
#         """Ensure singleton pattern for DBManager."""
#         if cls._instance is None:
#             cls._instance = super(DBManager, cls).__new__(cls)
#             cls._instance._initialized = False
#         return cls._instance
    
#     def __init__(self, config_path: Optional[str] = None):
#         """Initialize the DB manager.
        
#         Args:
#             config_path: Path to the config file. If None, uses default locations.
#         """
#         # Only initialize once
#         if getattr(self, '_initialized', False):
#             return
            
#         # Configuration properties
#         self._config: Dict[str, Any] = {}
#         self._current_env: str = os.getenv('ENV', 'default')
        
#         # Connection storage - flattened to be keyed by connection name
#         self._connection_config: Dict[str, ConnectionConfig] = {}
#         self._connections: Dict[str, DatabaseClient] = {}
        
#         # Load configuration
#         self._load_config(config_path)
        
#         # Initialize connections based on current environment
#         self._initialize_connections()
        
#         self._initialized = True
    
#     def _load_config(self, config_path: Optional[str] = None) -> None:
#         """Load configuration from YAML file.
        
#         Args:
#             config_path: Path to config file. If None, searches in default locations.
#         """
#         if config_path is None:
#             # Default locations to search for config
#             default_locations = [
#                 './db_config.yaml',
#                 './config/db_config.yaml',
#                 os.path.expanduser('~/.tradertools/db_config.yaml'),
#             ]
            
#             for location in default_locations:
#                 if os.path.exists(location):
#                     config_path = location
#                     break
            
#             if config_path is None:
#                 raise FileNotFoundError("Could not find db_config.yaml in default locations")
        
#         self._config = DEFAULT_CONFIG.copy()
#         with open(config_path, 'r') as f:
#             self._config = always_merger.merge(self._config, yaml.safe_load(f))
            
        
#         # Validate config structure
#         if 'connections' not in self._config or 'environments' not in self._config:
#             raise ValueError("Invalid config format: must have 'connections' and 'environments' sections")
            
#         # Parse connection details
#         self._parse_connection_configs()
    
#     def _parse_connection_configs(self) -> None:
#         """Parse connection details from config."""
#         for conn_name, conn_config in self._config['connections'].items():
#             db_type = conn_config.pop('db', None)
#             if not db_type:
#                 log.warning(f"Connection {conn_name} missing 'db' type, skipping")
#                 continue
                
#             try:
#                 connection_configs = self._create_conn_config(db_type, conn_config)
#                 # Store connection details with conn_name as the key
#                 self._connection_config[conn_name] = connection_configs
#             except Exception as e:
#                 log.error(f"Error creating connection details for {conn_name}: {e}")
    
#     def _create_conn_config(self, db_type: str, config: Dict[str, Any]) -> ConnectionConfig:
#         """Create an appropriate connection details instance based on the type.
        
#         Args:
#             db_type: Database type string
#             config: Connection configuration
            
#         Returns:
#             Database-specific connection details instance
            
#         Raises:
#             ValueError: If database type is not supported
#         """
#         # Get the registry from the ConnectionConfig class
#         registry = ConnectionConfig._registry
        
#         if db_type not in registry:
#             raise ValueError(f"2. Unsupported database type: {db_type}")
            
#         conn_cls = registry[db_type]
#         return conn_cls.from_dict(config)
    
#     def _initialize_connections(self) -> None:
#         """Initialize connections based on current environment."""
#         if self._current_env not in self._config['environments']:
#             log.warning(f"Environment {self._current_env} not found in config, using 'default'")
#             self._current_env = 'default'
            
#         env_config = self._config['environments'][self._current_env]
        
#         # Initialize connections for each database type reference in the environment
#         for db_type_alias, conn_name in env_config.items():
#             try:
#                 if conn_name not in self._connection_config:
#                     log.warning(f"Connection {conn_name} not found in config")
#                     continue
                    
#                 # Close existing connection if any
#                 if db_type_alias in self._connections:
#                     self._disconnect(db_type_alias)
                    
#                 # Create the new connection
#                 self._create_connection(db_type_alias, conn_name)
#             except Exception as e:
#                 log.error(f"Error initializing connection {conn_name} for {db_type_alias}: {e}")
    
#     def _create_connection(self, db_type_alias: str, conn_name: str) -> None:
#         """Create a new database connection.
        
#         Args:
#             db_type_alias: Database type alias from environment config (e.g., 'pg')
#             conn_name: Name of the connection configuration
#         """
#         # Get connection details
#         connection_configs = self._connection_config.get(conn_name)
#         if not connection_configs:
#             raise ValueError(f"Connection details not found for {conn_name}")
            
#         conn_args = connection_configs.connection_args()
        
#         # Determine the database type from the connection details
#         db_type_str = connection_configs.type_id
        
#         # Create connection based on type
#         try:
#             if db_type_str == DbType.POSTGRES.value:
#                 if 'host' not in conn_args:
#                     raise ValueError("Missing 'host' in connection arguments")
#                 if 'port' not in conn_args:
#                     raise ValueError("Missing 'port' in connection arguments")
#                 if 'user' not in conn_args:
#                     raise ValueError("Missing 'user' in connection arguments")
#                 if 'password' not in conn_args:
#                     raise ValueError("Missing 'password' in connection arguments")
#                 if 'database' not in conn_args:
#                     raise ValueError("Missing 'database' in connection arguments")
                
#                 client = PostgresClient(
#                     name=conn_name,
#                     host=conn_args.get('host'),
#                     port=conn_args.get('port'),
#                     user=conn_args.get('user'),
#                     password=conn_args.get('password'),
#                     database=conn_args.get('database')
#                 )
#                 # Connect synchronously
#                 client.connect()
                
#                 # Store the connection with the alias from environment
#                 self._connections[db_type_alias] = client
                
#                 log.info(f"Connected to {db_type_str} using alias {db_type_alias}: {conn_name}")
            
#             # Add support for other database types here
#             # elif db_type_str == DbType.REDIS.value:
#             #     ...
                
#             else:
#                 log.warning(f"3. Unsupported database type: {db_type_str}")
                
#         except Exception as e:
#             log.error(f"Error connecting to {conn_name} for alias {db_type_alias}: {e}")
#             raise
    
#     def _disconnect(self, alias: str) -> None:
#         """Disconnect a database connection.
        
#         Args:
#             alias: Database alias to disconnect
#         """
#         if alias in self._connections:
#             try:
#                 conn = self._connections[alias]
#                 if hasattr(conn, 'disconnect') and callable(conn.disconnect):
#                     conn.disconnect()
#                     log.info(f"Disconnected from {alias}")
#             except Exception as e:
#                 log.error(f"Error disconnecting from {alias}: {e}")
            
#             # Remove the connection
#             del self._connections[alias]
    
#     def switch_environment(self, env_name: str) -> None:
#         """Switch to a different environment.
        
#         Args:
#             env_name: Name of the environment to switch to
#         """
#         if env_name not in self._config['environments']:
#             raise ValueError(f"Unknown environment: {env_name}")
            
#         if env_name == self._current_env:
#             log.info(f"Already using environment: {env_name}")
#             return
            
#         log.info(f"Switching from {self._current_env} to {env_name}")
#         self._current_env = env_name
        
#         # Re-initialize connections for the new environment
#         self._initialize_connections()
    
#     def get_connection(self, alias: str) -> Any:
#         """Get the connection for a database alias.
        
#         Args:
#             alias: Database alias to get connection for (e.g., 'pg')
            
#         Returns:
#             The connection client
#         """
#         if alias not in self._connections:
#             raise ValueError(f"No connection for alias {alias}")
            
#         return self._connections[alias]
    
#     @property
#     def pg(self) -> PostgresClient:
#         """Get the PostgreSQL connection.
        
#         Returns:
#             PostgreSQL client
            
#         Raises:
#             ValueError: If no PostgreSQL connection exists
#         """
#         return self.get_connection('pg')
    
#     # Add other property accessors for different database types
#     # @property
#     # def redis(self) -> RedisClient:
#     #     return self.get_connection('rmd')
    
#     def close_all(self) -> None:
#         """Close all database connections."""
#         for alias in list(self._connections.keys()):
#             self._disconnect(alias)
            
#         log.info("All connections closed")
        
#     def switch_connection(self, alias: str, conn_name_or_config: Union[str, ConnectionConfig, Dict[str, Any]]) -> Any:
#         """Switch an individual database connection.
        
#         Args:
#             alias: Database alias from environment (e.g., 'pg')
#             conn_name_or_config: Either a connection name from config, a ConnectionConfig instance,
#                                   or a dictionary of connection parameters
                                  
#         Returns:
#             The new connection
            
#         Raises:
#             ValueError: If connection name not found or details are invalid
#         """
#         # First, close existing connection if any
#         if alias in self._connections:
#             self._disconnect(alias)
        
#         # Handle different input types
#         if isinstance(conn_name_or_config, str):
#             # It's a connection name from config
#             conn_name = conn_name_or_config
            
#             if conn_name not in self._connection_config:
#                 raise ValueError(f"Connection {conn_name} not found in config")
                
#             # Create the connection from stored details
#             self._create_connection(alias, conn_name)
            
#         elif isinstance(conn_name_or_config, ConnectionConfig):
#             # It's already a ConnectionConfig instance
#             connection_configs = conn_name_or_config
            
#             # Store the details with a generated name
#             name = f"custom_{alias}_{len(self._connection_config)}"
#             self._connection_config[name] = connection_configs
            
#             # Create the connection
#             self._create_connection(alias, name)
            
#         elif isinstance(conn_name_or_config, dict):
#             # It's a dictionary of connection parameters
#             config = conn_name_or_config
            
#             # Must include db_type in the config
#             if 'db' not in config:
#                 raise ValueError("Missing 'db' type in connection parameters")
                
#             db_type = config.pop('db')
            
#             # Create ConnectionConfig instance
#             connection_configs = self._create_conn_config(db_type, config)
            
#             # Store the details with a generated name
#             name = f"custom_{alias}_{len(self._connection_config)}"
#             self._connection_config[name] = connection_configs
            
#             # Create the connection
#             self._create_connection(alias, name)
            
#         else:
#             raise ValueError(f"Invalid connection details type: {type(conn_name_or_config)}")
            
#         # Return the new connection
#         return self._connections[alias]
    
#     def list_configs(self, db_type: Optional[Union[DbType, str]] = None) -> Dict[str, ConnectionConfig]:
#         """List available connection details.
        
#         Args:
#             db_type: Optional database type to filter by. If None, lists all connections.
            
#         Returns:
#             Dictionary of connection details by name
#         """
#         # If db_type is provided, filter connections by type
#         if db_type is not None:
#             db_type_str = db_type.value if isinstance(db_type, DbType) else db_type
            
#             # Filter connections that match the requested db_type
#             return {
#                 name: details for name, details in self._connection_config.items()
#                 if details.__class__.__name__.split('ConnectionConfig')[0].upper() == db_type_str
#             }
        
#         # Otherwise return all connections
#         return self._connection_config


# # Helper functions for global access

# def switch_env(env_name: str) -> None:
#     """Convenience function to switch to a different database environment.
    
#     Args:
#         env_name: Name of the environment to switch to
#     """
#     global env
#     manager.switch_environment(env_name)
#     env = manager._current_env  # Update the global reference

# def list_envs() -> Dict[str, Dict[str, str]]:
#     """Get all available database environments from the configuration.
    
#     Returns:
#         Dictionary of environment configurations
#     """
#     return manager._config.get('environments', {})

# def switch_pg(connection_config: Union[str, ConnectionConfig, Dict[str, Any]]):
#     """Switch the PostgreSQL connection without changing the whole environment.
    
#     Args:
#         connection_config: Either a connection name from config, a ConnectionConfig instance,
#                      or a dictionary of connection parameters
        
#     Returns:
#         The new PostgreSQL client
#     """
#     manager.switch_connection('pg', connection_config)

# def list_configs(db_type: Optional[Union[DbType, str]] = None) -> Dict[str, ConnectionConfig]:
#     """List available connection details.
    
#     Args:
#         db_type: Optional database type to filter by. If None, lists all connections.
        
#     Returns:
#         Dictionary of connection details by name
#     """
#     return manager.list_configs(db_type)
    
    
# def get_config(name: str) -> ConnectionConfig:
#     """Get a specific connection configuration by name.
    
#     Args:
#         name: Name of the connection configuration
#     Returns:
#         The connection configuration instance
#     """
#     if name not in manager._connection_config:
#         raise ValueError(f"Connection {name} not found in config")
        
#     return manager._connection_config[name]

    
    
    
# # Create the global database manager instance
# config_path = os.getenv('DB_CONFIG_PATH')
# manager = DBManager(config_path)
    
     
# def current_env() -> str:
#     """Get the current environment name."""
#     return manager._current_env if manager else 'default'

# def pg() -> PostgresClient:
#     """Get the PostgreSQL client."""
#     if manager:
#         return manager.pg
#     else:
#         raise ValueError("Database manager not initialized")
     
     
# # # Create global instance at module level
# # pg: PostgresClient = manager.pg  # Create global instance at module level
# # env: str = manager._current_env  # Create environment reference