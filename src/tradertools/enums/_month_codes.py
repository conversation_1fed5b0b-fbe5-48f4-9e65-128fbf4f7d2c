__all__ = ['MonthCode']

from enum import Enum

month_names = {
    1: 'Jan',
    2: 'Feb',
    3: 'Mar',
    4: 'Apr',
    5: 'May',
    6: 'Jun',
    7: 'Jul',
    8: 'Aug',
    9: 'Sep',
    10: 'Oct',
    11: 'Nov',
    12: 'Dec'
}


class MonthCode(Enum):
    F = 1
    G = 2
    H = 3
    J = 4
    K = 5
    M = 6
    N = 7
    Q = 8
    U = 9
    V = 10
    X = 11
    Z = 12
    
    @classmethod
    def from_int(cls, month: int) -> 'MonthCode':
        for code in cls:
            if code.value == month:
                return code
        raise ValueError(f"Invalid month number: {month}")
    
    @classmethod
    def from_str(cls, month_code: str) -> 'MonthCode':
        for code in cls:
            if code.name == month_code:
                return code
        raise ValueError(f"Invalid month code: {month_code}")

    @classmethod
    def int2str(cls, month: int) -> str:
        for code in cls:
            if code.value == month:
                return code.name
        raise ValueError(f"Invalid month number: {month}")

    @classmethod
    def int2name(cls, month: int) -> str:
        for code in cls:
            if code.value == month:
                return month_names[code.value]
        raise ValueError(f"Invalid month number: {month}")

    @classmethod
    def str2int(cls, month_code: str) -> int:
        for code in cls:
            if code.name == month_code:
                return code.value
        raise ValueError(f"Invalid month code: {month_code}")
    
    @classmethod
    def str2name(cls, month_code: str) -> str:
        for code in cls:
            if code.name == month_code:
                return month_names[code.value]
        raise ValueError(f"Invalid month code: {month_code}")
    
    @classmethod
    def name2int(cls, month_name: str) -> int:
        for code in cls:
            if month_names[code.value] == month_name:
                return code.value
        raise ValueError(f"Invalid month name: {month_name}")
    
    @classmethod
    def name2str(cls, month_name: str) -> str:
        for code in cls:
            if month_names[code.value] == month_name:
                return code.name
        raise ValueError(f"Invalid month name: {month_name}")

    @property
    def month(self) -> str:
        
        return month_names[self.value]