"""tradertools package."""
__version__ = "0.1.2"

import os
import getpass


from tradertools.enums import *
from tradertools import instruments
from tradertools import marketdata
from tradertools import clients
from tradertools import strategies
from tradertools import trade



__all__ = [
    "__version__",
    "instruments",
    "marketdata",
    "clients",
    "strategies",
    "trade",
]



# set user if no env 
if not os.getenv("USER"):
    os.environ["USER"] = getpass.getuser()

# set title
if not os.getenv("TITLE"):
    os.environ["TITLE"] = "tradertools"
    
# set env
if not os.getenv("ENV"):
    os.environ["ENV"] = "DEVELOPMENT"