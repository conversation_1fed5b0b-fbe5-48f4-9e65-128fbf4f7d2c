__all__ = ['RedisClient']
from uuid import uuid4
from typing import Callable, Optional, List, Dict, Any
import logging
import time
from threading import RLock
from contextlib import contextmanager

import redis
import orjson

log = logging.getLogger(__name__)

class RedisException(Exception):
    """Base exception for Redis client errors"""
    pass

class RedisClient:
    """
    Redis Client for listening to channels and patterns.
    Provides passthrough methods for the redis-py client.
    Also creates a pubsub listener thread for the client.
    """

    def __init__(
        self, 
        ip: str,
        port: int = 6379, 
        password: str = None, 
        db: int = 0,
        name: str = None,
        socket_timeout: float = 5.0,
        socket_connect_timeout: float = 2.0,
        max_connections: int = 10,
    ):
        """
        Initialize the Redis client.

        Args:
            ip (str): The IP address of the Redis server.
            port (int): The port of the Redis server.
            password (str): The password for the Redis server.
            db (int): The Redis database number.
            name (str): The name of the Redis client.
            socket_timeout (float): The socket timeout for Redis operations.
            socket_connect_timeout (float): The socket connection timeout.
            socket_keepalive (bool): Whether to use socket keepalive.
            max_connections (int): Maximum number of connections in the pool.
        """
        if ip is None:
            raise ValueError("Must provide an IP address")

        self.name = name or f"redis-client-{uuid4()}"
        self.ip = ip
        self.port = port
        self.db = db
        self._lock = RLock()  # For thread safety
        self._closed = False
        self._pubsub_started = False
        self._pubsub = None
        self._listen_thread = None
        
        # Create connection pool with sensible defaults
        self.pool = redis.ConnectionPool(
            host=ip,
            port=port,
            password=password,
            db=db,
            decode_responses=True,
            socket_keepalive=True,
            socket_timeout=socket_timeout,
            socket_connect_timeout=socket_connect_timeout,
            max_connections=max_connections,
            client_name=self.name,
        )
        
        self.client = redis.Redis(connection_pool=self.pool)
        # Initial connection test
        self.client.ping()

    @property
    def pubsub(self):
        """Lazy initialization of pubsub client"""
        if self._pubsub is None:
            self._pubsub = self.client.pubsub()
        return self._pubsub


    def _start_pubsub_thread(self):
        """Start pubsub thread with automatic recovery."""
        def exception_handler(ex, pubsub, thread):
            if not self._closed:
                thread.stop()
                thread.join(timeout=1.0)
                self._pubsub_started = False
                self._start_pubsub_thread()
        
        self.pubsub.get_message(timeout=0.5)  # Connection test
        self._listen_thread = self.pubsub.run_in_thread(
            sleep_time=0.001,
            daemon=True,
            exception_handler=exception_handler
        )
        self._pubsub_started = True


    def __enter__(self): return self
    def __exit__(self, *args): self.close()
    def __del__(self):
        """Destructor to ensure resources are properly cleaned up."""
        try:
            self.close()
        except:
            pass  # Suppress errors during garbage collection
    

    @contextmanager
    def _error_handling(self, operation: str):
        """Context manager for handling Redis errors consistently."""
        try:
            yield
        except redis.RedisError as e:
            log.error(f"Redis error during {operation}: {e}")
            # Check if we need to reconnect
            try:
                if self._closed:
                    return
                # Try to ping to see if connection is alive
                self.client.ping()
            except redis.ConnectionError:
                log.warning(f"Connection lost during {operation}, attempting to reconnect")
                try:
                    # Get a fresh connection from pool
                    self.client.connection_pool.reset()
                except Exception as reconnect_err:
                    log.error(f"Failed to reconnect: {reconnect_err}")
            # Re-raise as our custom exception
            raise RedisException(f"Error during {operation}: {e}") from e
    
    def check_health(self) -> dict:
        """
        Check if the Redis connection is healthy.
        
        Returns:
            dict: Health status information
        """
        status = {
            "connected": False,
            "latency_ms": None,
            "pool_info": {
                "max_connections": self.pool.max_connections
            }
        }
        
        try:
            start = time.time()
            self.client.ping()
            end = time.time()
            status["connected"] = True
            status["latency_ms"] = round((end - start) * 1000, 2)
        except Exception as e:
            status["error"] = str(e)
        
        return status
    
    def subscribe(self, channel: str, callback: Callable) -> None:
        """
        Subscribe to a Redis channel.

        Args:
            channel (str): The channel to subscribe to.
            callback (Callable): Callback function for received messages.
        """
        with self._error_handling("subscribe"):
            # Start pubsub thread if needed
            if not self._pubsub_started:
                self.start_pubsub_thread()
            self.pubsub.subscribe(**{channel: callback})
            log.debug(f"Subscribed to channel: {channel}")
    
    def psubscribe(self, pattern: str, callback: Callable) -> None:
        """
        Subscribe to a Redis channel pattern.
        
        Args:
            pattern (str): The pattern to subscribe to.
            callback (Callable): Callback function for received messages.
        """
        with self._error_handling("psubscribe"):
            # Start pubsub thread if needed
            if not self._pubsub_started:
                self.start_pubsub_thread()
            self.pubsub.psubscribe(**{pattern: callback})
            log.debug(f"Subscribed to pattern: {pattern}")
    
    def unsubscribe(self, channel: str) -> None:
        """
        Unsubscribe from a Redis channel.
        
        Args:
            channel (str): The channel to unsubscribe from.
        """
        with self._error_handling("unsubscribe"):
            self.pubsub.unsubscribe(channel)
            log.debug(f"Unsubscribed from channel: {channel}")
    
    def punsubscribe(self, pattern: str) -> None:
        """
        Unsubscribe from a Redis channel pattern.
        
        Args:
            pattern (str): The pattern to unsubscribe from.
        """
        with self._error_handling("punsubscribe"):
            self.pubsub.punsubscribe(pattern)
            log.debug(f"Unsubscribed from pattern: {pattern}")

    def publish(self, channel: str, msg: str) -> int:
        """
        Publish a message to a Redis channel with retry on connection failure.

        Args:
            channel (str): The channel to publish the message to.
            msg (str): The message to publish.
            
        Returns:
            int: Number of clients that received the message
            
        Raises:
            RedisException: If publishing fails after retry
            TypeError: If msg is not a string
        """
        if not isinstance(msg, str):
            raise TypeError(f'Expected str, got {type(msg)}')
        
        try:
            with self._error_handling("publish"):
                log.info(f'Publishing to {channel}: {msg[:100]}{"..." if len(msg) > 100 else ""}')
                return self.client.publish(channel, msg)
        except RedisException:
            # One retry attempt on connection failure
            log.warning(f"Retrying publish to {channel} after connection error")
            with self._error_handling("publish_retry"):
                return self.client.publish(channel, msg)
        

    def request_response(self, req_channel: str, request: dict, timeout: float = 5.0, 
                         response_validator: Callable[[dict], bool] = None) -> Optional[Any]:
        """
        Makes a request to the given channel and waits for a response on a temporary channel.
        
        Args:
            req_channel (str): The channel a response is requested from
            request (dict): Dict containing request (will be copied, not modified)
            timeout (float): Maximum time to wait for a response in seconds
            response_validator (Callable): Optional function to validate response format
            
        Returns:
            Optional[Any]: The processed data response or None if timed out
            
        Raises:
            RedisException: If there's an error communicating with Redis or if the response is invalid
            ValueError: If the request is not a valid dictionary
        """
        if not isinstance(request, dict):
            raise ValueError(f"Request must be a dictionary, got {type(request)}")
            
        # Create request ID for tracing
        request_id = str(uuid4())[:8]
        log.debug(f"[{request_id}] Starting request to {req_channel}")
        
        # Copy request to avoid modifying the original
        request_copy = request.copy()
        
        # Create a response channel with prefix for better organization
        respchannel = f"resp:{request_id}:{str(uuid4())}"
        request_copy['respchannel'] = respchannel
        p = None
        
        try:
            p = self.client.pubsub()
            
            with self._error_handling(f"[{request_id}] subscribe"):
                p.subscribe(respchannel)
                # Ignore the subscription confirmation message with explicit timeout
                subscription_msg = p.get_message(timeout=0.5)
                if not subscription_msg:
                    raise RedisException(f"[{request_id}] Failed to confirm subscription to {respchannel}")
            
            with self._error_handling(f"[{request_id}] publish"):
                request_json = orjson.dumps(request_copy).decode('utf-8')
                publish_result = self.client.publish(req_channel, request_json)
                if publish_result == 0:
                    log.warning(f"[{request_id}] No clients received the message on {req_channel}")
            
            # Wait for the response with timeout using adaptive polling
            start_time = time.time()
            poll_interval = 0.05  # Start with 50ms polling
            max_poll_interval = 0.5  # Max 500ms polling
            
            while time.time() - start_time < timeout:
                with self._error_handling(f"[{request_id}] get_message"):
                    # Use adaptive polling interval
                    msg = p.get_message(timeout=poll_interval)
                    
                    # If no message, increase poll interval with exponential backoff
                    if not msg:
                        poll_interval = min(poll_interval * 1.5, max_poll_interval)
                        continue
                        
                    if msg.get('type') == 'message':
                        try:
                            res = orjson.loads(msg['data'])
                            
                            # Use custom validator if provided
                            if response_validator is not None:
                                if not response_validator(res):
                                    log.warning(f"[{request_id}] Response failed custom validation: {res}")
                                    raise RedisException(f"[{request_id}] Response failed custom validation")
                            # Otherwise use default validation
                            elif 'response' not in res or 'data' not in res.get('response', {}):
                                log.warning(f"[{request_id}] Received malformed response: {res}")
                                raise RedisException(f"[{request_id}] Malformed response: missing response.data field")
                                
                            log.debug(f"[{request_id}] Received valid response after {time.time() - start_time:.3f}s")
                            return res['response']['data']
                        except (ValueError, TypeError) as e:
                            log.error(f"[{request_id}] Failed to parse response: {e}")
                            raise RedisException(f"[{request_id}] Failed to parse response: {e}")
            
            # Timeout occurred
            elapsed = time.time() - start_time
            log.warning(f"[{request_id}] Request to {req_channel} timed out after {elapsed:.3f}s/{timeout:.1f}s")
            return None
            
        finally:
            # Clean up the temporary channel
            if p:
                try:
                    log.debug(f"[{request_id}] Cleaning up PubSub resources")
                    p.unsubscribe(respchannel)
                    p.close()
                except Exception as e:
                    log.error(f"[{request_id}] Error during cleanup: {e}")
                    # Don't suppress the original exception if there is one

    def close(self) -> None:
        """
        Close the Redis client and clean up resources.
        """
        with self._lock:
            if self._closed:
                return
            
            if self.listen_thread and self._pubsub_started:
                try:
                    self.listen_thread.stop()
                    self.listen_thread.join(timeout=1.0)
                    log.info(f'{self.name} stopped listening')
                except Exception as e:
                    log.warning(f"Error stopping listener thread: {e}")
            
            try:
                self.pubsub.close()
            except Exception as e:
                log.warning(f"Error closing pubsub: {e}")
                
            try:
                self.client.close()
                log.info(f'{self.name} client closed')
            except Exception as e:
                log.warning(f"Error closing client: {e}")
            
            self._closed = True

    def stop(self) -> None:
        """
        Stop listening for Redis messages.
        """
        with self._lock:
            if self.listen_thread and self._pubsub_started:
                try:
                    self.listen_thread.stop()
                    self.listen_thread.join(timeout=1.0)
                    self._pubsub_started = False
                    log.info(f'{self.name} stopped listening')
                except Exception as e:
                    log.warning(f"Error stopping listener thread: {e}")

    # Basic Redis operations with error handling
    def set(self, key: str, value: str, ex: int = None, px: int = None, nx: bool = False, xx: bool = False) -> bool:
        """
        Set a Redis key to a value with optional expiration.

        Args:
            key (str): The Redis key to set.
            value (str): The value to set the key to.
            ex (int): Expire time in seconds.
            px (int): Expire time in milliseconds.
            nx (bool): Only set the key if it does not already exist.
            xx (bool): Only set the key if it already exists.

        Returns:
            bool: True if the operation succeeded.
        """
        with self._error_handling("set"):
            return self.client.set(key, value, ex=ex, px=px, nx=nx, xx=xx)

    def get(self, key: str) -> Optional[str]:
        """
        Get the value of a Redis key.

        Args:
            key (str): The Redis key to get the value of.

        Returns:
            Optional[str]: The value of the Redis key or None if it doesn't exist.
        """
        with self._error_handling("get"):
            return self.client.get(key)

    #### LIST METHODS ####
    def lpush(self, key: str, *values: str) -> int:
        """
        Push one or multiple values to a Redis list, starting from the left.

        Args:
            key (str): The Redis key of the list to push to.
            *values (str): The values to push to the list.

        Returns:
            int: The length of the list after the push operation.
        """
        with self._error_handling("lpush"):
            return self.client.lpush(key, *values)

    def rpush(self, key: str, *values: str) -> int:
        """
        Push one or multiple values to a Redis list, starting from the right.

        Args:
            key (str): The Redis key of the list to push to.
            *values (str): The values to push to the list.

        Returns:
            int: The length of the list after the push operation.
        """
        with self._error_handling("rpush"):
            return self.client.rpush(key, *values)

    def lpop(self, key: str) -> Optional[str]:
        """
        Remove and get the first element in a Redis list.

        Args:
            key (str): The Redis key of the list to pop from.

        Returns:
            Optional[str]: The first element in the list, or None if the list is empty.
        """
        with self._error_handling("lpop"):
            return self.client.lpop(key)

    def rpop(self, key: str) -> Optional[str]:
        """
        Remove and get the last element in a Redis list.

        Args:
            key (str): The Redis key of the list to pop from.

        Returns:
            Optional[str]: The last element in the list, or None if the list is empty.
        """
        with self._error_handling("rpop"):
            return self.client.rpop(key)

    def llen(self, key: str) -> int:
        """
        Get the length of a Redis list.

        Args:
            key (str): The Redis key of the list to get the length of.

        Returns:
            int: The length of the Redis list.
        """
        with self._error_handling("llen"):
            return self.client.llen(key)

    def lrange(self, key: str, start: int, end: int) -> List[str]:
        """
        Get a range of elements from a Redis list.

        Args:
            key (str): The Redis key of the list to get the range from.
            start (int): The index of the first element to get.
            end (int): The index of the last element to get.

        Returns:
            List[str]: A list of elements from the Redis list.
        """
        with self._error_handling("lrange"):
            return self.client.lrange(key, start, end)
    
    def ltrim(self, key: str, start: int, end: int) -> None:
        """
        Trim a Redis list to the specified range.

        Args:
            key (str): The Redis key of the list to trim.
            start (int): The index of the first element to keep.
            end (int): The index of the last element to keep.
        """
        with self._error_handling("ltrim"):
            self.client.ltrim(key, start, end)
    
    def lrem(self, key: str, count: int, value: str) -> int:
        """
        Remove elements from a Redis list.

        Args:
            key (str): The Redis key of the list to remove elements from.
            count (int): The number of elements to remove:
                         count > 0: Remove elements equal to value moving from head to tail.
                         count < 0: Remove elements equal to value moving from tail to head.
                         count = 0: Remove all elements equal to value.
            value (str): The value to remove.

        Returns:
            int: The number of elements that were removed.
        """
        with self._error_handling("lrem"):
            return self.client.lrem(key, count, value)
    
    def lindex(self, key: str, index: int) -> Optional[str]:
        """
        Get an element from a Redis list by its index.

        Args:
            key (str): The Redis key of the list to get the element from.
            index (int): The index of the element to get.

        Returns:
            Optional[str]: The element at the specified index in the Redis list, or None if index is out of range.
        """
        with self._error_handling("lindex"):
            return self.client.lindex(key, index)
    
    def lset(self, key: str, index: int, value: str) -> None:
        """
        Set the value of an element in a Redis list.

        Args:
            key (str): The Redis key of the list to set the element in.
            index (int): The index of the element to set.
            value (str): The value to set the element to.
        """
        with self._error_handling("lset"):
            self.client.lset(key, index, value)
        
    def linsert(self, key: str, where: str, refvalue: str, value: str) -> int:
        """
        Insert an element into a Redis list.

        Args:
            key (str): The Redis key of the list to insert the element into.
            where (str): Where to insert the element (BEFORE or AFTER).
            refvalue (str): The value to insert the new element before or after.
            value (str): The value to insert.

        Returns:
            int: The length of the list after the insert operation.
        """
        with self._error_handling("linsert"):
            return self.client.linsert(key, where, refvalue, value)
    
    def lpushx(self, key: str, value: str) -> int:
        """
        Push a value to a Redis list, starting from the left, only if the list exists.

        Args:
            key (str): The Redis key of the list to push to.
            value (str): The value to push to the list.

        Returns:
            int: The length of the list after the push operation.
        """
        with self._error_handling("lpushx"):
            return self.client.lpushx(key, value)
    
    #### HASH METHODS ####
    def hgetall(self, key: str) -> Dict[str, str]:
        """
        Get all the fields and values of a Redis hash.

        Args:
            key (str): The Redis key of the hash to get the fields and values of.

        Returns:
            Dict[str, str]: A dictionary of the fields and values of the Redis hash.
        """
        with self._error_handling("hgetall"):
            return self.client.hgetall(key)
    
    def hget(self, key: str, field: str) -> Optional[str]:
        """
        Get the value of a field in a Redis hash.

        Args:
            key (str): The Redis key of the hash to get the value from.
            field (str): The field to get the value of.

        Returns:
            Optional[str]: The value of the field in the Redis hash or None if field doesn't exist.
        """
        with self._error_handling("hget"):
            return self.client.hget(key, field)

    def hset(self, key: str, field: str, value: str) -> int:
        """
        Set the value of a field in a Redis hash.

        Args:
            key (str): The Redis key of the hash to set the value in.
            field (str): The field to set the value of.
            value (str): The value to set the field to.

        Returns:
            int: 1 if field is a new field in the hash and value was set, 0 if existing field.
        """
        with self._error_handling("hset"):
            return self.client.hset(key, field, value)

    def hmset(self, key: str, mapping: Dict[str, str]) -> bool:
        """
        Set multiple fields and values in a Redis hash.

        Args:
            key (str): The Redis key of the hash to set the fields and values in.
            mapping (Dict[str, str]): A dictionary of fields and values to set.

        Returns:
            bool: True if the operation succeeded.
        """
        with self._error_handling("hmset"):
            return self.client.hmset(key, mapping)
        
    def hdel(self, key: str, *fields: str) -> int:
        """
        Delete one or more fields from a Redis hash.

        Args:
            key (str): The Redis key of the hash to delete fields from.
            *fields (str): The fields to delete.

        Returns:
            int: The number of fields that were removed from the hash.
        """
        with self._error_handling("hdel"):
            return self.client.hdel(key, *fields)
    
    def hexists(self, key: str, field: str) -> bool:
        """
        Determine if a field exists in a Redis hash.

        Args:
            key (str): The Redis key of the hash to check for the field.
            field (str): The field to check for.

        Returns:
            bool: True if the field exists in the hash, False if it does not.
        """
        with self._error_handling("hexists"):
            return self.client.hexists(key, field)
    
    def hkeys(self, key: str) -> List[str]:
        """
        Get all the fields in a Redis hash.

        Args:
            key (str): The Redis key of the hash to get the fields of.

        Returns:
            List[str]: A list of the fields in the Redis hash.
        """
        with self._error_handling("hkeys"):
            return self.client.hkeys(key)
    
    def hvals(self, key: str) -> List[str]:
        """
        Get all the values in a Redis hash.

        Args:
            key (str): The Redis key of the hash to get the values of.

        Returns:
            List[str]: A list of the values in the Redis hash.
        """
        with self._error_handling("hvals"):
            return self.client.hvals(key)
    
    def hlen(self, key: str) -> int:
        """
        Get the number of fields in a Redis hash.

        Args:
            key (str): The Redis key of the hash to get the number of fields of.

        Returns:
            int: The number of fields in the Redis hash.
        """
        with self._error_handling("hlen"):
            return self.client.hlen(key)
        
    #### Stream Methods ####
    def xadd(self, stream: str, data: Dict[str, str], maxlen: int = 1000) -> str:
        """
        Add a message to a Redis stream.

        Args:
            stream (str): The name of the stream to add the message to.
            data (Dict[str, str]): The message to add to the stream.
            maxlen (int): Maximum length of the stream.

        Returns:
            str: The ID of the message that was added to the stream.
        """
        with self._error_handling("xadd"):
            return self.client.xadd(stream, data, maxlen=maxlen)
    
    def xread(self, streams: Dict[str, str], count: int = 1, block: int = 0) -> List[Dict[str, List]]:
        """
        Read messages from one or more Redis streams.

        Args:
            streams (Dict[str, str]): A dictionary of stream names and the ID of the last message read.
            count (int): The maximum number of messages to read.
            block (int): The number of milliseconds to block while waiting for new messages.

        Returns:
            List[Dict[str, List]]: A list of dictionaries containing stream name and messages.
        """
        with self._error_handling("xread"):
            return self.client.xread(streams, count=count, block=block)
    
    def xlen(self, stream: str) -> int:
        """
        Get the number of messages in a Redis stream.

        Args:
            stream (str): The name of the stream to get the number of messages of.

        Returns:
            int: The number of messages in the Redis stream.
        """
        with self._error_handling("xlen"):
            return self.client.xlen(stream)
    
    def xrange(self, stream: str, start: str = '-', end: str = '+', count: int = None) -> List:
        """
        Get a range of messages from a Redis stream.

        Args:
            stream (str): The name of the stream to get the messages from.
            start (str): The ID of the first message to get.
            end (str): The ID of the last message to get.
            count (int): The maximum number of messages to get.

        Returns:
            List: A list of messages from the Redis stream.
        """
        with self._error_handling("xrange"):
            return self.client.xrange(stream, start, end, count=count)
    
    def xrevrange(self, stream: str, end: str = '+', start: str = '-', count: int = None) -> List:
        """
        Get a range of messages from a Redis stream in reverse order.

        Args:
            stream (str): The name of the stream to get the messages from.
            end (str): The ID of the last message to get.
            start (str): The ID of the first message to get.
            count (int): The maximum number of messages to get.

        Returns:
            List: A list of messages from the Redis stream.
        """
        with self._error_handling("xrevrange"):
            return self.client.xrevrange(stream, end, start, count=count)
    
    def xtrim(self, stream: str, maxlen: int, approximate: bool = False) -> int:
        """
        Trim a Redis stream to a maximum length.

        Args:
            stream (str): The name of the stream to trim.
            maxlen (int): The maximum length of the stream.
            approximate (bool): Whether to use approximate trimming.

        Returns:
            int: The number of messages that were removed from the stream.
        """
        with self._error_handling("xtrim"):
            return self.client.xtrim(stream, maxlen, approximate=approximate)
    
    def xdel(self, stream: str, *ids: str) -> int:
        """
        Delete one or more messages from a Redis stream.

        Args:
            stream (str): The name of the stream to delete messages from.
            *ids (str): The IDs of the messages to delete.

        Returns:
            int: The number of messages that were removed from the stream.
        """
        with self._error_handling("xdel"):
            return self.client.xdel(stream, *ids)
    
    def scan_iter(self, match: str = None, count: int = None) -> List[str]:
        """
        Get an iterator over keys in Redis.

        Args:
            match (str): The pattern to match keys against.
            count (int): The maximum number of keys per batch.

        Returns:
            List[str]: A list of matching keys.
        """
        with self._error_handling("scan_iter"):
            ret = []
            for key in self.client.scan_iter(match=match, count=count):
                ret.append(key)
            return ret
 
    def delete(self, *keys: str) -> int:
        """
        Delete one or more keys from Redis.

        Args:
            *keys (str): The keys to delete.

        Returns:
            int: The number of keys that were removed.
        """
        with self._error_handling("delete"):
            return self.client.delete(*keys)
