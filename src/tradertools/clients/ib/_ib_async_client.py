__all__ = ["IBClient"]

import ib_async as iba


class IBClient:
    
    def __init__(self, host: str = "127.0.0.1", port: int = 7497, client_id: int = 1):
        if client_id == 1:
            raise ValueError("Client ID 1 is reserved for internal use. Please use a different client ID.")
        self.host = host
        self.port = port
        self.client_id = client_id
        self.ib = iba.IB()
        
        
    def connect(self, **kwargs):
        self.ib.connect(self.host, self.port, self.client_id, timeout=15, **kwargs)
        return self.ib
        
    def is_connected(self) -> bool:
        return self.ib.isConnected()
        
        
    def disconnect(self):
        self.ib.disconnect()