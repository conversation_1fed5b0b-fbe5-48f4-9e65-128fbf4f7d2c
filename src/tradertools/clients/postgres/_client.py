__all__ = ["PostgresClient"] #, "create_postgres_client"

import logging
from typing import List, Dict, Any, Optional, Union, <PERSON>ple

import psycopg
from psycopg.rows import dict_row

# from tradertools.db.postgres._connection_config import PostgresConnDetails


log = logging.getLogger(__name__)





class PostgresClient:
    """PostgreSQL client implementing both synchronous and asynchronous operations.
    
    Uses psycopg3 for both synchronous and asynchronous operations.
    """
    
    def __init__(self, name: str,**kwargs) -> None:
    # def __init__(self, name: str, host: str="localhost", port: int=5432, user: str="jack", password: str="jack", database: str="main", minimum_pool_size: int = 1, maximum_pool_size: int = 1) -> None:
        """Initialize the PostgreSQL client.
        
        Args:
            name: Client name
            host: Database server hostname
            port: Database server port
            user: Database username
            password: Database password
            database: Database name
        """

        if 'host' not in kwargs:
            raise ValueError("Missing 'host' in connection arguments")
        if 'port' not in kwargs:
            raise ValueError("Missing 'port' in connection arguments")
        if 'user' not in kwargs:
            raise ValueError("Missing 'user' in connection arguments")
        if 'password' not in kwargs:
            raise ValueError("Missing 'password' in connection arguments")
        if 'database' not in kwargs:
            raise ValueError("Missing 'database' in connection arguments")
        
        self.kwargs = kwargs.copy()
        
        self.name = name
        self.host = kwargs.pop('host')
        self.port = kwargs.pop('port')
        self.user = kwargs.pop('user')
        self.password = kwargs.pop('password')
        self.database = kwargs.pop('database')
        self.minimum_pool_size = kwargs.pop('minimum_pool_size', 1)
        self.maximum_pool_size = kwargs.pop('maximum_pool_size', 5)
        self.conninfo = (
            f"host={self.host} "
            f"port={self.port} "
            f"user={self.user} "
            f"password={self.password} "
            f"dbname={self.database}"
        )
        
        self.sync_conn = None
        self.async_conn = None
    
    #---------------------------------------------------------------------------
    # Synchronous connection methods
    #---------------------------------------------------------------------------
    def connect(self, autocommit: bool = True, **kwargs) -> None:
        """Establish synchronous connection to PostgreSQL using psycopg3.
        
        Args:
            autocommit: Whether to enable autocommit mode (default: True)
            **kwargs: Additional connection parameters to pass to psycopg3
        """
        self.sync_conn = psycopg.connect(self.conninfo, **kwargs)
        
        # Set autocommit mode
        self.sync_conn.autocommit = autocommit
        
        # Set timezone to UTC
        with self.sync_conn.cursor() as cursor:
            cursor.execute("SET timezone TO 'UTC'")
            
        # Only commit if not in autocommit mode
        if not autocommit:
            self.sync_conn.commit()
        
        
    def disconnect(self) -> None:
        """Close synchronous connection to PostgreSQL."""
        if self.sync_conn:
            self.sync_conn.close()
            self.sync_conn = None
        
            
    def is_connected(self) -> bool:
        """Check if synchronous connection is established and valid."""
        if not self.sync_conn:
            return False
        try:
            # Try a simple query to check connection
            with self.sync_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                return True
        except Exception:
            return False
    
    
    #---------------------------------------------------------------------------
    # Asynchronous connection methods (implementing abstract methods)
    #---------------------------------------------------------------------------
    async def connect_async(self, **kwargs) -> None:
        """Establish asynchronous connection to PostgreSQL using psycopg3."""
        self.async_conn = await psycopg.AsyncConnection.connect(self.conninfo, **kwargs)
        # Set timezone to UTC
        async with self.async_conn.cursor() as cursor:
            await cursor.execute("SET timezone TO 'UTC'")
        await self.async_conn.commit()
        
        
    async def disconnect_async(self) -> None:
        """Close asynchronous connection to PostgreSQL."""
        if self.async_conn:
            await self.async_conn.close()
            self.async_conn = None
            
            
    async def is_connected_async(self) -> bool:
        """Check if asynchronous connection is established and valid."""
        if not self.async_conn:
            return False
        try:
            # Try a simple query to check connection
            async with self.async_conn.cursor() as cursor:
                await cursor.execute("SELECT 1")
                return True
        except Exception:
            return False
    
    
    
    #---------------------------------------------------------------------------
    # Helper execution methods
    #---------------------------------------------------------------------------
    def execute(self, query: str, params: Optional[Union[Dict[str, Any], Tuple, List[Any]]] = None) -> Tuple[List[Dict[str, Any]], int]:
        """Execute a SQL query synchronously.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Tuple containing (results, affected_rows)
        """
        if not self.sync_conn:
            log.error("Not connected to database")
            return [], 0
            
        try:
            with self.sync_conn.cursor(row_factory=dict_row) as cursor:
                cursor.execute(query.encode('utf-8'), params)
                
                # Check if this is a SQL query that returns results
                if cursor.description:
                    results = cursor.fetchall()
                    return list(results), cursor.rowcount
                else:
                    # For non-returning queries like INSERT, UPDATE, DELETE
                    self.sync_conn.commit()
                    return [], cursor.rowcount
        except Exception as e:
            # Roll back transaction on error but don't re-raise
            if self.sync_conn:
                try:
                    self.sync_conn.rollback()
                except Exception as rollback_error:
                    log.error(f"Error during rollback: {rollback_error}")
                    
            log.error(f"Query execution error: {e}")
            return [], 0  # Return empty results and zero affected rows
    
    
    def execute_many(self, query: str, params_seq: List[Union[Dict[str, Any], Tuple, List[Any]]]) -> int:
        """Execute a SQL query with multiple sets of parameters using psycopg3's executemany.
        
        Args:
            query: SQL query string
            params_seq: List of parameter sets
            
        Returns:
            Number of affected rows
        """
        if not self.sync_conn:
            log.error("Not connected to database")
            return 0
            
        try:
            with self.sync_conn.cursor() as cursor:
                cursor.executemany(query.encode('utf-8'), params_seq)
                self.sync_conn.commit()
                return cursor.rowcount
        except Exception as e:
            # Roll back transaction on error but don't re-raise
            if self.sync_conn:
                try:
                    self.sync_conn.rollback()
                except Exception as rollback_error:
                    log.error(f"Error during rollback in execute_many: {rollback_error}")
            raise e # Raise the error to be handled by the caller+
            
            
    async def execute_async(self, query: str, params: Optional[Union[Dict[str, Any], Tuple, List[Any]]] = None) -> Tuple[List[Dict[str, Any]], int]:
        """Execute a SQL query asynchronously.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Tuple containing (results, affected_rows)
        """
        if not self.async_conn:
            log.error("Not connected to database asynchronously")
            return [], 0
            
        try:
            async with self.async_conn.cursor(row_factory=dict_row) as cursor:
                await cursor.execute(query.encode('utf-8'), params)
                
                # Check if this is a SQL query that returns results
                if cursor.description:
                    results = await cursor.fetchall()
                    return list(results), cursor.rowcount
                else:
                    # For non-returning queries like INSERT, UPDATE, DELETE
                    await self.async_conn.commit()
                    return [], cursor.rowcount
        except Exception as e:
            # Roll back transaction on error but don't re-raise
            if self.async_conn:
                try:
                    await self.async_conn.rollback()
                except Exception as rollback_error:
                    log.error(f"Error during async rollback: {rollback_error}")
                    
            log.error(f"Async query execution error: {e}")
            return [], 0  # Return empty results and zero affected rows
        
    
    async def execute_many_async(self, query: str, params_seq: List[Union[Dict[str, Any], Tuple, List[Any]]]) -> int:
        """Execute a SQL query with multiple sets of parameters asynchronously.
        
        Args:
            query: SQL query string
            params_seq: List of parameter sets
            
        Returns:
            Number of affected rows
        """
        if not self.async_conn:
            log.error("Not connected to database asynchronously")
            return 0
            
        try:
            async with self.async_conn.cursor() as cursor:
                await cursor.executemany(query.encode('utf-8'), params_seq)
                await self.async_conn.commit()
                return cursor.rowcount
        except Exception as e:
            # Roll back transaction on error but don't re-raise
            if self.async_conn:
                try:
                    await self.async_conn.rollback()
                except Exception as rollback_error:
                    log.error(f"Error during async rollback in execute_many_async: {rollback_error}")
                    
            log.error(f"Async batch execution error: {e}")
            return 0  # Return zero affected rows
        
    #---------------------------------------------------------------------------
    # Synchronous data methods
    #---------------------------------------------------------------------------
    def table_exists(self, schema: str, table_name: str) -> bool:
        """Check if a table exists synchronously.
        
        Args:
            table_name: Name of the table
            schema: Name of the schema
            
        Returns:
            True if table exists, False otherwise
        """
        # query = (
        #     "SELECT 1 FROM information_schema.tables "
        #     "WHERE table_schema = %s AND table_name = %s"
        # )
        query = (
            "SELECT EXISTS ( "
            "    SELECT FROM information_schema.tables  "
            "    WHERE table_schema = %s  "
            "    AND table_name = %s "
            ");"
        )
        results, _ = self.execute(query, (schema, table_name))
        if results[0]['exists']: 
            return True
        else:
            return False
        
    
    
    def list_tables(self, schema: str) -> List[Dict]:
        query = (
        f"""SELECT 
                c.relname as table_name,
                CASE c.relkind 
                    WHEN 'r' THEN 'BASE TABLE'
                    WHEN 'v' THEN 'VIEW'
                    WHEN 'm' THEN 'MATERIALIZED VIEW'
                END as table_type
            FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE n.nspname = '{schema}'
            AND c.relkind IN ('r', 'v', 'm')
            ORDER BY table_type, table_name;
        """
        )
        results, _ = self.execute(query, None)
        return results
        
        
    
    def query(self, query: str, params: Optional[Union[Dict[str, Any], Tuple, List[Any]]] = None) -> List[Dict[str, Any]]:
        """Perform a SQL query synchronously.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of row dictionaries
        """
        results, _ = self.execute(query, params)
        return results
    
    
    def insert(self, table: str, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
            returning: Optional[Union[str, List[str]]] = None) -> Union[List[Dict[str, Any]], int]:
        """Insert one or more rows synchronously.
        
        Args:
            table: Table name
            data: Dict or list of dicts with column:value pairs to insert
            returning: Column(s) to return after insert
            
        Returns:
            Either the returned rows or the number of affected rows
        """
        if not data:
            return [] if returning else 0
            
        # Convert single dict to list
        if isinstance(data, dict):
            data = [data]
            
        # Extract columns from first dict (assuming all dicts have same keys)
        columns = list(data[0].keys())
        columns_str = ", ".join([f'"{col}"' for col in columns])
        
        # Prepare RETURNING clause if needed
        if isinstance(returning, list):
            quoted_returning = [f'"{col}"' for col in returning]
            returning_str = ", ".join(quoted_returning)
        else:
            returning_str = f'"{returning}"'
        returning_clause = f" RETURNING {returning_str}" if returning else ""
        
        # For single row inserts, use standard execute with named parameters
        if len(data) == 1:
            named_placeholders = [f"%({col})s" for col in columns]
            placeholders_str = ", ".join(named_placeholders)
            query = (
                f"INSERT INTO {table} "
                f"({columns_str}) VALUES ({placeholders_str})"
                f"{returning_clause}"
            )
            
            results, affected_rows = self.execute(query, data[0])
            return results if results else affected_rows
        else:
            # For multiple rows without returning clause, use executemany
            if not returning_clause:
                pos_placeholders = ["%s" for _ in columns]
                placeholders_str = ", ".join(pos_placeholders)
                query = f"INSERT INTO {table} ({columns_str}) VALUES ({placeholders_str})"
                
                # Convert list of dicts to list of tuples for executemany
                params_seq = []
                for row in data:
                    params_seq.append([row[col] for col in columns])
                
                return self.execute_many(query, params_seq)
            else:
                # For inserts with RETURNING, process one by one and combine results
                all_results = []
                
                named_placeholders = [f"%({col})s" for col in columns]
                placeholders_str = ", ".join(named_placeholders)
                query = (
                    f"INSERT INTO {table} "
                    f"({columns_str}) VALUES ({placeholders_str})"
                    f"{returning_clause}"
                )
                
                for row in data:
                    results, _ = self.execute(query, row)
                    if isinstance(results, list):
                        all_results.extend(results)
                    elif isinstance(results, dict):
                        all_results.append(results)
                    # If results is int or None, ignore
                        
                return all_results
            
            
    
    def insert_bulk(self, table: str, data: List[Dict[str, Any]], 
                   chunk_size: int = 1000,
                   returning: Optional[Union[str, List[str]]] = None) -> Union[List[Dict[str, Any]], int]:
        """Insert multiple rows in chunks for better performance.
        
        Args:
            table: Table name
            data: List of dicts with column:value pairs to insert
            chunk_size: Number of records to insert in each batch
            returning: Column(s) to return after insert
            
        Returns:
            Either the returned rows (if returning specified) or the total number of affected rows
        """
        if not data:
            return [] if returning else 0
            
        total_affected = 0
        all_results = []
        
        # Process in chunks
        for i in range(0, len(data), chunk_size):
            chunk = data[i:i + chunk_size]
            
            if returning:
                results = self.insert(table, chunk, returning)
                if isinstance(results, list):
                    all_results.extend(results)
            else:
                affected = self.insert(table, chunk)
                if isinstance(affected, int):
                    total_affected += affected
                
        return all_results if returning else total_affected
    
    
    def upsert(self, table: str, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
            conflict_target: Union[str, List[str]], 
            returning: Optional[Union[str, List[str]]] = None) -> Union[List[Dict[str, Any]], int]:
        """Perform an upsert operation (INSERT ... ON CONFLICT).
        
        Args:
            table: Table name
            data: Dict or list of dicts with column:value pairs
            conflict_target: Column name(s) or constraint name for conflict detection
            returning: Column(s) to return after upsert
            
        Returns:
            Either the returned rows or the number of affected rows
        """
        if not data:
            return [] if returning else 0
            
        # Convert single dict to list
        if isinstance(data, dict):
            data = [data]
            
        # Extract columns from first dict (assuming all dicts have same keys)
        columns = list(data[0].keys())
        columns_str = ", ".join([f'"{col}"' for col in columns]) 
        
        # Determine if conflict_target is a constraint name or column(s)
        if isinstance(conflict_target, list):
            conflict_columns_quoted = [f'"{col}"' for col in conflict_target]
            conflict_columns_str = ', '.join(conflict_columns_quoted)
            conflict_clause = f"({conflict_columns_str})"
            # Exclude conflict columns from update
            update_columns = [col for col in columns if col not in conflict_target]
        else:
            # Check if it looks like a constraint name with improved detection
            if (conflict_target.startswith("pk_") or 
                conflict_target.startswith("uq_") or 
                conflict_target.startswith("idx_") or
                conflict_target.endswith("_pk") or 
                conflict_target.endswith("_key") or 
                conflict_target.endswith("_pkey") or 
                conflict_target.endswith("_constraint") or 
                "_constraint_" in conflict_target):
                conflict_clause = f"ON CONSTRAINT {conflict_target}"
            else:
                # Quote the column name
                conflict_clause = f"(\"{conflict_target}\")"
            
            # For constraint conflicts, update all columns
            update_columns = columns
        
        # Build the DO UPDATE SET clause with quoted column names
        update_clause = ", ".join([f"\"{col}\" = EXCLUDED.\"{col}\"" for col in update_columns])
        action_clause = f"DO UPDATE SET {update_clause}"
            
        # For returning clause
        returning_clause = ""
        if returning:
            if isinstance(returning, list):
                returning_str = ", ".join([f"\"{col}\"" for col in returning])
            else:
                returning_str = f"\"{returning}\""
            returning_clause = f" RETURNING {returning_str}"
        
        # For single row upserts
        if len(data) == 1:
            placeholders = ", ".join([f"%({col})s" for col in columns])
            query = f"""
                INSERT INTO {table} ({columns_str}) 
                VALUES ({placeholders})
                ON CONFLICT {conflict_clause} {action_clause}
                {returning_clause}
            """
            
            results, affected_rows = self.execute(query, data[0])
            return results if results else affected_rows
        else:
            # For multiple rows without returning, use executemany
            if not returning_clause:
                placeholders = ", ".join([f"%s"] * len(columns))
                query = f"""
                    INSERT INTO {table} ({columns_str}) 
                    VALUES ({placeholders})
                    ON CONFLICT {conflict_clause} {action_clause}
                """
                
                # Convert list of dicts to list of tuples
                params_seq = []
                for row in data:
                    params_seq.append([row[col] for col in columns])
                
                return self.execute_many(query, params_seq)
            else:
                # For upserts with RETURNING, process one by one
                all_results = []
                for row in data:
                    row_results = self.upsert(table, row, conflict_target, returning)
                    if isinstance(row_results, list):
                        all_results.extend(row_results)
                        
                return all_results
    
    #---------------------------------------------------------------------------
    # Asynchronous data methods
    #---------------------------------------------------------------------------
    async def query_async(self, query: str, params: Optional[Union[Dict[str, Any], Tuple, List[Any]]] = None) -> List[Dict[str, Any]]:
        """Perform a SQL query asynchronously.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of row dictionaries
        """
        results, _ = await self.execute_async(query, params)
        return results
    
    async def insert_async(self, table: str, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                         returning: Optional[Union[str, List[str]]] = None) -> Union[List[Dict[str, Any]], int]:
        """Insert one or more rows asynchronously.
        
        Args:
            table: Table name
            data: Dict or list of dicts with column:value pairs to insert
            returning: Column(s) to return after insert
            
        Returns:
            Either the returned rows or the number of affected rows
        """
        if not data:
            return [] if returning else 0
            
        # Convert single dict to list
        if isinstance(data, dict):
            data = [data]
            
        # Extract columns from first dict (assuming all dicts have same keys)
        columns = list(data[0].keys())
        columns_str = ", ".join(columns)
        
        # Prepare RETURNING clause if needed
        if isinstance(returning, list):
            quoted_returning = [f'"{col}"' for col in returning]
            returning_str = ", ".join(quoted_returning)
        else:
            returning_str = f'"{returning}"'
        returning_clause = f" RETURNING {returning_str}" if returning else ""
        
        # For single row inserts, use standard execute with named parameters
        if len(data) == 1:
            named_placeholders = [f"%({col})s" for col in columns]
            placeholders_str = ", ".join(named_placeholders)
            query = (
                f"INSERT INTO {table} "
                f"({columns_str}) VALUES ({placeholders_str})"
                f"{returning_clause}"
            )
            
            results, affected_rows = await self.execute_async(query, data[0])
            return results if results else affected_rows
        else:
            # For multiple rows without returning clause, use executemany with positional parameters
            if not returning_clause:
                pos_placeholders = ["%s" for _ in columns]
                placeholders_str = ", ".join(pos_placeholders)
                query = f"INSERT INTO {table} ({columns_str}) VALUES ({placeholders_str})"
                
                # Convert list of dicts to list of tuples
                params_seq = []
                for row in data:
                    params_seq.append([row[col] for col in columns])
                
                return await self.execute_many_async(query, params_seq)
            else:
                # For inserts with RETURNING, process one by one and combine results
                all_results = []
                
                named_placeholders = [f"%({col})s" for col in columns]
                placeholders_str = ", ".join(named_placeholders)
                query = (
                    f"INSERT INTO {table} "
                    f"({columns_str}) VALUES ({placeholders_str})"
                    f"{returning_clause}"
                )
                
                for row in data:
                    results, _ = await self.execute_async(query, row)
                    if isinstance(results, list):
                        all_results.extend(results)
                        
                return all_results
    
    async def insert_bulk_async(self, table: str, data: List[Dict[str, Any]], 
                              chunk_size: int = 1000,
                              returning: Optional[Union[str, List[str]]] = None) -> Union[List[Dict[str, Any]], int]:
        """Insert multiple rows in chunks asynchronously for better performance.
        
        Args:
            table: Table name
            data: List of dicts with column:value pairs to insert
            chunk_size: Number of records to insert in each batch
            returning: Column(s) to return after insert
            
        Returns:
            Either the returned rows (if returning specified) or the total number of affected rows
        """
        if not data:
            return [] if returning else 0
            
        total_affected = 0
        all_results = []
        
        # Process in chunks
        for i in range(0, len(data), chunk_size):
            chunk = data[i:i + chunk_size]
            
            if returning:
                results = await self.insert_async(table, chunk, returning)
                if isinstance(results, list):
                    all_results.extend(results)
                else:
                    # If results is int or None, ignore
                    pass
                
            else:
                affected = await self.insert_async(table, chunk)
                if isinstance(affected, int):
                    total_affected += affected
                
        return all_results if returning else total_affected
    
    async def upsert_async(self, table: str, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                         conflict_target: Union[str, List[str]], 
                         returning: Optional[Union[str, List[str]]] = None) -> Union[List[Dict[str, Any]], int]:
        """Perform an upsert operation (INSERT ... ON CONFLICT) asynchronously.
        
        Args:
            table: Table name
            data: Dict or list of dicts with column:value pairs
            conflict_target: Column name(s) or constraint name for conflict detection
            returning: Column(s) to return after upsert
            
        Returns:
            Either the returned rows or the number of affected rows
        """
        if not data:
            return [] if returning else 0
            
        # Convert single dict to list
        if isinstance(data, dict):
            data = [data]
            
        # Extract columns from first dict (assuming all dicts have same keys)
        columns = list(data[0].keys())
        columns_str = ", ".join(columns)
        
        # Determine if conflict_target is a constraint name or column(s)
        if isinstance(conflict_target, list):
            conflict_clause = f"({', '.join(conflict_target)})"
            # Exclude conflict columns from update
            update_columns = [col for col in columns if col not in conflict_target]
        else:
            # Check if it looks like a constraint name
            if conflict_target.startswith("pk_") or conflict_target.startswith("uq_") or conflict_target.startswith("idx_"):
                conflict_clause = f"ON CONSTRAINT {conflict_target}"
            else:
                conflict_clause = f"({conflict_target})"
            # For constraint conflicts, update all columns
            update_columns = columns
                
        # Build the DO UPDATE SET clause
        update_clause = ", ".join([f"{col} = EXCLUDED.{col}" for col in update_columns])
        action_clause = f"DO UPDATE SET {update_clause}"
            
        # For returning clause
        returning_clause = ""
        if returning:
            if isinstance(returning, list):
                returning_str = ", ".join(returning)
            else:
                returning_str = returning
            returning_clause = f" RETURNING {returning_str}"
        
        # For single row upserts
        if len(data) == 1:
            placeholders = ", ".join([f"%({col})s" for col in columns])
            query = f"""
                INSERT INTO {table} ({columns_str}) 
                VALUES ({placeholders})
                ON CONFLICT {conflict_clause} {action_clause}
                {returning_clause}
            """
            
            results, affected_rows = await self.execute_async(query, data[0])
            return results if results else affected_rows
        else:
            # For multiple rows without returning, try executemany
            if not returning_clause:
                placeholders = ", ".join([f"%s"] * len(columns))
                query = f"""
                    INSERT INTO {table} ({columns_str}) 
                    VALUES ({placeholders})
                    ON CONFLICT {conflict_clause} {action_clause}
                """
                
                # Convert list of dicts to list of tuples
                params_seq = []
                for row in data:
                    params_seq.append([row[col] for col in columns])
                
                try:
                    return await self.execute_many_async(query, params_seq)
                except Exception as e:
                    # Fall back to row-by-row if executemany fails with ON CONFLICT
                    # Make sure we roll back any partial transaction
                    logging.error(f"Bulk insert failed: {e}")
                    
                    if self.async_conn:
                        await self.async_conn.rollback()
                    
                    # Try row-by-row approach
                    total_affected = 0
                    for row in data:
                        affected = await self.upsert_async(table, row, conflict_target)
                        if (isinstance(affected, int)):
                            total_affected += affected
                    return total_affected
            else:
                # For upserts with RETURNING, process one by one
                all_results = []
                for row in data:
                    row_results = await self.upsert_async(table, row, conflict_target, returning)
                    if isinstance(row_results, list):
                        all_results.extend(row_results)
                        
                return all_results
            
            
    @property
    def connection(self) -> Union[psycopg.Connection, None]:
        """Return the underlying connection object."""
        if self.sync_conn:
            return self.sync_conn
        else:
            return None
    
    @property
    def conn_string(self) -> str:
        """Return the connection string."""
        return f"postgresql+psycopg://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
            
            
    def __repr__(self) -> str:
        """Return string representation of the PostgreSQL client."""
        sync_status = "connected" if self.sync_conn is not None else "disconnected"
        async_status = "connected" if self.async_conn is not None else "disconnected"
        return f"PostgresClient(name='{self.name}', host='{self.host}', port={self.port}, " \
                f"database='{self.database}', user='{self.user}', " \
                f"sync={sync_status}, async={async_status})"
