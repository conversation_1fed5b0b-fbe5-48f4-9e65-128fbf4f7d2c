__all__ = [
    "_manager", 
    "get_pg_client",
    "get_nats_client",
    "get_ib_client",
    "list_connections", 
    "list_active_clients", 
    "reload_config", 
    "reconnect", 
    "disconnect"
]

import os
import yaml
import logging
# from pathlib import Path
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional, Type, TypeVar, cast, Protocol, runtime_checkable

from tradertools.clients.postgres import PostgresClient
from tradertools.clients.natsio import NatsClient
from tradertools.clients.ib import IBClient


log = logging.getLogger(__name__)


class ServiceType(Enum):
    POSTGRES = "postgres"
    NATS = "nats"
    REDIS = "redis"
    IB = "ib"


@dataclass
class ConnectionConfig:
    """Configuration for a single connection"""
    name: str  # 'production', 'development', etc.
    service_type: ServiceType  # 'postgres', 'nats', etc.
    config: Dict[str, Any]


@runtime_checkable
class Connectable(Protocol):
    def connect(self, **kwargs) -> None: ...
    def disconnect(self) -> None: ...
    def is_connected(self) -> bool: ...


T = TypeVar('T', bound=Connectable)


class ConnectionManager:
    """_manager for all connections"""
    
    def __init__(self, config_path: Optional[str] = None):
        
        self._configs: Dict[str, ConnectionConfig] = {}
        self._connections: Dict[str, Connectable] = {}
        
        if config_path:
            self.load_config(config_path)
        else:
            log.info("No connection config path provided, using localhost for all configurations")
        
        
        
    def load_config(self, config_path: str) -> None:
        """Load connection configurations from a file"""
        
        # check if file exists
        if config_path == 'connections.yaml' and not os.path.exists(config_path):
            for _ in range(2):
                config_path = os.path.join("../", config_path)
                if os.path.exists(config_path):
                    break
            else:
                raise FileNotFoundError(f"Connection config file not found: {config_path}")
        
        with open(config_path, 'r') as f:
            conns = yaml.safe_load(f)
            
        for conn_name, conn_config in conns.get("connections", {}).items():
            
            service_type = ServiceType(conn_config["service_type"])
            del conn_config["service_type"]
            self._configs[conn_name] = ConnectionConfig(conn_name, service_type, conn_config)
        
            
            
    def _get_client(self, conn_name: str, conn_type: Optional[Type[T]] = None) -> T:
        """Get a connection by name"""
        if conn_name not in self._configs:
            raise ValueError(f"Connection {conn_name} not found")
        
        # Check if the requested type matches the service type before connecting
        if conn_type is not None:
            config = self._configs[conn_name]
            expected_service_type = None
            
            # Map connection types to service types
            if conn_type.__name__ == "PostgresClient":
                expected_service_type = ServiceType.POSTGRES
            elif conn_type.__name__ == "RedisClient":
                expected_service_type = ServiceType.REDIS
            elif conn_type.__name__ == "NatsClient":
                expected_service_type = ServiceType.NATS
            elif conn_type.__name__ == "IBClient":
                expected_service_type = ServiceType.IB
            # Add more mappings as needed
            
            if expected_service_type and config.service_type != expected_service_type:
                raise ValueError(f"Connection '{conn_name}' is of service type {config.service_type.value}, not compatible with {conn_type.__name__}")
        
        if conn_name not in self._connections:
            self._connect(conn_name)
            
        conn = self._connections[conn_name]
        
        # Double-check type after connection
        if conn_type is not None and not isinstance(conn, conn_type):
            raise ValueError(f"Connection '{conn_name}' is not of type {conn_type.__name__}")
        
        return cast(T, conn)
        
        
        
    def _connect(self, conn_name: str) -> None:
        """Connect to a database"""
        conn_config = self._configs[conn_name]
        
        if conn_config.service_type == ServiceType.POSTGRES:
            from tradertools.clients.postgres import PostgresClient
            client = PostgresClient(conn_name, **conn_config.config)
            self._connections[conn_name] = client
            client.connect()
        elif conn_config.service_type == ServiceType.NATS:
            from tradertools.clients.natsio import NatsClient
            client = NatsClient(**conn_config.config)
            self._connections[conn_name] = client
            client.connect()
        elif conn_config.service_type == ServiceType.IB:
            from tradertools.clients.ib import IBClient
            client = IBClient(**conn_config.config)
            self._connections[conn_name] = client
            client.connect()
        else:
            raise ValueError(f"Unsupported service type: {conn_config.service_type}")
        
        
        log.debug(f"Connected to {conn_name}")
        
        
        
    def _reconnect(self, conn_name: str) -> None:
        """Reconnect to a database"""
        self._disconnect(conn_name)
        self._connect(conn_name)
        
        
        
    def _disconnect(self, conn_name: str) -> None:
        """Disconnect from a database"""
        if conn_name in self._connections:
            self._connections[conn_name].disconnect()
            del self._connections[conn_name]
            log.debug(f"Disconnected from {conn_name}")
        
        
        
        
config_path = os.getenv('CONNECTION_CONFIG_PATH', 'connections.yaml')
_manager = ConnectionManager(config_path)
        
        
        
def list_connections() -> Dict[str, ConnectionConfig]:
    """List all available connections"""
    return _manager._configs

def list_active_clients() -> Dict[str, Connectable]:
    """List all active clients"""
    return _manager._connections

def _get_client(conn_name: str, conn_type: Optional[Type[T]] = None) -> T:
    """Get a connection by name"""
    return _manager._get_client(conn_name, conn_type)

def get_pg_client(conn_name: str) -> PostgresClient:
    """Get a PostgreSQL client"""
    return _get_client(conn_name, PostgresClient)

def get_nats_client(conn_name: str) -> NatsClient:
    """Get a NATS client"""
    return _get_client(conn_name, NatsClient)

def get_ib_client(conn_name: str) -> IBClient:
    """Get an IB client"""
    return _get_client(conn_name, IBClient)




def reload_config(config_path: str) -> None:
    """Reload connection configurations from a file"""
    _manager.load_config(config_path)


def reconnect(conn_name: str) -> None:
    """Reconnect to a database"""
    _manager._connect(conn_name)
    
def disconnect(conn_name: str) -> None:
    """Disconnect from a database"""
    _manager._disconnect(conn_name)
