__all__ = ["NatsClient"]
import asyncio
import logging
import threading
import or<PERSON><PERSON>
from uuid import uuid4
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import Thread<PERSON>oolExecutor

import nats
from nats.aio.client import Client as NATSClient
from nats.js.client import JetStreamContext
from nats.js.api import ConsumerConfig
from nats.js.api import DeliverPolicy, AckPolicy
from nats.errors import TimeoutError as NATSTimeoutError
from nats.js.errors import NoKeysError

log = logging.getLogger(__name__)

class NatsClient:
    """Lightweight NATS client with synchronous interface."""
    
    def __init__(self, **kwargs):
    # def __init__(self, servers: List[str] = ["nats://localhost:4222"]):
    
        if 'servers' not in kwargs:
            raise ValueError("NATS client requires 'servers' argument")
    
        self.servers = kwargs.pop('servers')
        self.nc: NATSClient
        self.js: JetStreamContext
        self._loop = None
        self._thread: threading.Thread
        self._running = False
        self._executor = ThreadPoolExecutor(max_workers=1)
        self._lock = threading.Lock()
        self._loop_ready = threading.Event()
        
    
    def _start_background_loop(self):
        """Start the event loop in a background thread."""
        self._loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._loop)
        # Signal that the loop is ready
        self._loop_ready.set()
        self._loop.run_forever()
    
    def _run_coroutine(self, coro):
        """Run a coroutine in the event loop and return its result."""
        if not self._loop or not self._running:
            raise RuntimeError("NATS client is not running")
        
        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        return future.result()
    
    def connect(self, **kwargs) -> None:
        """Connect to NATS server synchronously."""
        with self._lock:
            if self._running:
                log.warning("NATS client is already running")
                return
            
            # Start the background thread with event loop
            self._thread = threading.Thread(target=self._start_background_loop, daemon=True)
            self._thread.start()
            self._running = True
            
            # Wait for the event loop to be ready
            self._loop_ready.wait(timeout=5.0)
            
            # Connect to NATS in the background loop
            async def _connect():
                try:
                    self.nc = await nats.connect(
                        servers=self.servers,
                        reconnect_time_wait=0.5,
                        max_reconnect_attempts=10,
                        ping_interval=20,
                        max_outstanding_pings=5,
                        connect_timeout=5.0,
                        allow_reconnect=True
                    )
                    self.js = self.nc.jetstream()
                    log.info(f"Connected to NATS servers: {self.servers}")
                except Exception as e:
                    log.error(f"Failed to connect to NATS: {e}")
                    self._running = False
                    if self._loop:
                        self._loop.call_soon_threadsafe(self._loop.stop)
                    raise
            
            self._run_coroutine(_connect())
    
    def disconnect(self) -> None:
        """Disconnect from NATS synchronously."""
        with self._lock:
            if not self._running:
                log.warning("NATS client is not running")
                return
            
            async def _disconnect():
                if self.nc:
                    try:
                        try:
                            await asyncio.wait_for(self.nc.drain(), timeout=5.0)
                            log.info("Drained and disconnected from NATS")
                        except asyncio.TimeoutError:
                            log.warning("Drain timed out, closing connection directly")
                            await self.nc.close()
                            log.info("Closed NATS connection")
                        except Exception as e:
                            log.warning(f"Error during drain: {e}, closing connection directly")
                            await self.nc.close()
                            log.info("Closed NATS connection")
                    except Exception as e:
                        log.error(f"Error disconnecting from NATS: {e}")
                    finally:
                        self.nc = None # type: ignore 
                        self.js = None # type: ignore
            
            try:
                self._run_coroutine(_disconnect())
            finally:
                self._running = False
                if self._loop:
                    self._loop.call_soon_threadsafe(self._loop.stop)
                self._thread.join(timeout=1.0)
                self._thread = None # type: ignore
                self._loop = None
    
    def publish(self, subject: str, data: Any) -> None:
        """Publish data to a NATS subject synchronously."""
        async def _publish():
            if not self.nc:
                raise RuntimeError("Not connected to NATS")
            
            try:
                payload = orjson.dumps(data) if not isinstance(data, bytes) else data
                await self.nc.publish(subject, payload)
            except Exception as e:
                log.error(f"Error publishing to {subject}: {e}")
                raise
        
        return self._run_coroutine(_publish())
    
    def publish_with_ack(self, subject: str, data: Any) -> Dict:
        """Publish data to a JetStream subject with acknowledgment synchronously."""
        async def _publish_with_ack():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                payload = orjson.dumps(data) if not isinstance(data, bytes) else data
                ack = await self.js.publish(subject, payload)
                return {
                    "stream": ack.stream,
                    "seq": ack.seq,
                    "domain": ack.domain
                }
            except Exception as e:
                log.error(f"Error publishing to JetStream {subject}: {e}")
                raise
        
        return self._run_coroutine(_publish_with_ack())
    
    def create_kv_bucket(self, bucket: str) -> None:
        """Create a KV bucket if it doesn't exist synchronously."""
        async def _create_kv_bucket():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                await self.js.create_key_value(bucket=bucket)
                log.info(f"Created KV bucket: {bucket}")
            except Exception as e:
                if "already exists" in str(e):
                    log.info(f"KV bucket {bucket} already exists")
                else:
                    log.error(f"Error creating KV bucket {bucket}: {e}")
                    raise
        
        return self._run_coroutine(_create_kv_bucket())
    
    def get_kv(self, bucket: str, key: str) -> Optional[Any]:
        """Get a value from a KV bucket synchronously."""
        async def _get_kv():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                kv = await self.js.key_value(bucket=bucket)
                entry = await kv.get(key)
                if entry and entry.value:
                    return orjson.loads(entry.value)
                return None
            except Exception as e:
                log.error(f"Error getting key {key} from bucket {bucket}: {e}")
                raise
        
        return self._run_coroutine(_get_kv())
    
    def put_kv(self, bucket: str, key: str, value: Any) -> None:
        """Put a value into a KV bucket synchronously."""
        async def _put_kv():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                kv = await self.js.key_value(bucket=bucket)
                payload = orjson.dumps(value)
                await kv.put(key, payload)
            except Exception as e:
                log.error(f"Error putting key {key} in bucket {bucket}: {e}")
                raise
        
        return self._run_coroutine(_put_kv())
    
    def delete_kv(self, bucket: str, key: str) -> None:
        """Delete a key from a KV bucket synchronously."""
        async def _delete_kv():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                kv = await self.js.key_value(bucket=bucket)
                await kv.delete(key)
            except Exception as e:
                log.error(f"Error deleting key {key} from bucket {bucket}: {e}")
                raise
        
        return self._run_coroutine(_delete_kv())
    
    
    def get_bucket(self, bucket: str) -> Dict[str, Any]:
        """Get all key-value pairs from a bucket as a dictionary synchronously.
        
        Args:
            bucket: Name of the KV bucket
            
        Returns:
            Dictionary containing all key-value pairs in the bucket
        
        Raises:
            RuntimeError: If JetStream is not initialized
            Exception: If there's an error getting keys from the bucket
        """
        async def _get_bucket():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                kv = await self.js.key_value(bucket=bucket)
                keys = await kv.keys()
                
                result = {}
                for key in keys:
                    entry = await kv.get(key)
                    if entry and entry.value:
                        result[key] = orjson.loads(entry.value)
                
                return result
            except NoKeysError:
                return {}
            
            except Exception as e:
                log.error(f"Error getting all keys from bucket {bucket}: {e}")
                raise
        
        return self._run_coroutine(_get_bucket())
    
    
    
    def get_last_stream_value(self, stream: str, subject: str) -> Optional[Dict]:
        """Get the last value from a stream for a specific subject synchronously."""
        async def _get_last_stream_value():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                # Create a consumer that starts at the last message
                consumer = await self.js.pull_subscribe(
                    subject=subject,
                    durable=f"last_msg_retriever_{subject.replace('.', '_')}",
                    stream=stream,
                    config=ConsumerConfig(
                        deliver_policy=DeliverPolicy.LAST,
                        ack_policy=AckPolicy.NONE,
                        max_deliver=1
                    )
                )
                
                # Try to get the last message
                try:
                    msgs = await consumer.fetch(batch=1, timeout=1)
                    if msgs and len(msgs) > 0:
                        msg = msgs[0]
                        data = orjson.loads(msg.data)
                        return data
                except NATSTimeoutError:
                    # No messages available
                    return None
                
                return None
            except Exception as e:
                log.error(f"Error getting last value from stream {stream}, subject {subject}: {e}")
                raise
        
        return self._run_coroutine(_get_last_stream_value())
    
    def create_stream(self, name: str, subjects: List[str], storage: str = "memory") -> None:
        """Create a stream if it doesn't exist synchronously."""
        async def _create_stream():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                # Check if stream exists
                try:
                    stream_info = await self.js.stream_info(name)
                    log.info(f"Stream {name} already exists with subjects: {stream_info.config.subjects}")
                    
                    # Check if our subjects are in the stream
                    existing_subjects = stream_info.config.subjects or []
                    missing_subjects = [s for s in subjects if s not in existing_subjects]
                    if missing_subjects:
                        log.warning(f"Subjects {missing_subjects} not in existing stream.")
                        
                except Exception as e:
                    # Stream doesn't exist, create it
                    if "stream not found" in str(e).lower():
                        await self.js.add_stream(
                            name=name, 
                            subjects=subjects,
                            storage=storage,
                            retention="limits",
                            discard="old",
                            max_age=86400,  # 1 day in seconds
                            max_msgs_per_subject=1000
                        )
                        log.info(f"Created stream {name} for subjects: {subjects}")
                    else:
                        # Some other error occurred
                        raise
            except Exception as e:
                log.error(f"Error creating stream {name}: {e}")
                raise
        
        return self._run_coroutine(_create_stream())
    
    def is_connected(self) -> bool:
        """Check if connected to NATS."""
        return self.nc is not None and self.nc.is_connected and self._running



    def request(self, subject: str, data: Any, timeout: float = 10.0) -> Optional[Any]:
        """Send a request and wait for a response synchronously.
        
        Args:
            subject (str): The subject to send the request to.
            data (Any): The data to send in the request.
            timeout (float): Maximum time to wait for a response in seconds.
        
        Returns:
            Optional[Any]: The response data or None if timed out.
        
        Raises:
            RuntimeError: If not connected to NATS.
            Exception: If there's an error during the request.
        """
        async def _request():
            if not self.nc:
                raise RuntimeError("Not connected to NATS")
            
            try:
                # add reply_to
                data['reply_to'] = f"{subject}.{uuid4()}"
                
                # Convert data to bytes if it's not already
                payload = orjson.dumps(data) if not isinstance(data, bytes) else data
                
                # Send request and wait for response
                log.debug(f"Sending request to {subject}: {str(data)[:100]}...")
                response = await self.nc.request(subject, payload, timeout=timeout)
                
                # Parse and return the response
                if response and response.data:
                    try:
                        return orjson.loads(response.data)
                    except Exception as e:
                        log.warning(f"Failed to parse response as JSON: {e}")
                        return response.data  # Return raw data if not JSON
                return None
                
            except NATSTimeoutError:
                log.warning(f"Request to {subject} timed out after {timeout}s")
                raise TimeoutError(f"Request to {subject} timed out after {timeout}s")
            except Exception as e:
                log.error(f"Error during request to {subject}: {e}")
                raise
        
        return self._run_coroutine(_request())


    def put_object(self, bucket: str, key: str, data: Any) -> None:
        """Put an object into an object store synchronously."""
        async def _put_object():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                obj_store = await self.js.object_store(bucket)
                payload = orjson.dumps(data) if not isinstance(data, bytes) else data
                await obj_store.put(name=key, data=payload)
                log.info(f"Object '{key}' added to bucket '{bucket}'")
            except Exception as e:
                log.error(f"Error putting object '{key}' in bucket '{bucket}': {e}")
                raise
        
        return self._run_coroutine(_put_object())


    def get_object(self, store: str, key: str) -> Optional[Any]:
        """Get an object from an object store synchronously."""
        async def _get_object():
            if not self.js:
                raise RuntimeError("JetStream not initialized")
            
            try:
                obj_store = await self.js.object_store(store)
                obj = await obj_store.get(name=key)
                if obj and obj.data:
                    await obj_store.delete(name=key)
                    return orjson.loads(obj.data)
                log.warning(f"Object '{key}' not found in store '{store}'")
                return None
            except Exception as e:
                log.error(f"Error getting object '{key}' from store '{store}': {e}")
                raise
        
        return self._run_coroutine(_get_object())
    
    
    
    def subscribe(self, subject: str, callback: Callable[[Any], None]) -> Any:
        """Subscribe to a NATS subject synchronously.
        
        Args:
            subject (str): The subject to subscribe to.
            callback (Callable): Function that will be called with each message.
                The callback should accept a single argument (the message).
        
        Returns:
            The subscription object that can be used to manage the subscription.
        
        Raises:
            RuntimeError: If not connected to NATS.
            Exception: If there's an error during subscription.
        """
        async def _subscribe():
            if not self.nc:
                raise RuntimeError("Not connected to NATS")
            
            try:
                # Create a subscription
                sub = await self.nc.subscribe(subject)
                
                # Start a background task to process messages
                asyncio.create_task(self._process_messages(sub, callback))
                
                log.info(f"Subscribed to {subject}")
                return sub
            except Exception as e:
                log.error(f"Error subscribing to {subject}: {e}")
                raise
        
        return self._run_coroutine(_subscribe())
    
    

    async def _process_messages(self, subscription, callback: Callable):
        """Process messages from a subscription and call the callback.
        
        This runs as a background task in the event loop.
        """
        try:
            async for msg in subscription.messages:
                try:
                    # Parse the message data if it's JSON
                    if isinstance(msg.data, bytes):
                        try:
                            data = orjson.loads(msg.data)
                        except Exception:
                            data = msg.data  # Keep as bytes if not valid JSON
                    else:
                        data = msg.data
                    
                    # Call the callback in the thread pool to avoid blocking the event loop
                    self._executor.submit(callback, data)
                except Exception as e:
                    log.error(f"Error processing message from {subscription.subject}: {e}")
        except Exception as e:
            log.error(f"Subscription processing error for {subscription.subject}: {e}")
            # Try to resubscribe or handle the error as needed