[project]
name = "tradertools"
version = "0.1.2"
description = ""
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"


[tool.poetry]
packages = [{include = "tradertools", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.10"
python-dotenv = ">=1.0.1,<2.0.0"
orjson = "^3.10.15"
redis = "^5.2.1"
hiredis = "^3.1.0"
pyyaml = "^6.0.2"
psycopg = {extras = ["binary"], version = "^3.2.5"}
deepmerge = "^2.0"
pandas = "^2.2.3"
nats-py = "^2.10.0"
tzdata = "^2025.2"
ibapi-wrapper = {git = "https://github.com/LuneAnalytics/ibapi-wrapper.git"}
ib-async = "^2.0.1"


[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
black = "^25.1.0"
isort = "^6.0.1"
flake8 = "^7.1.2"
mypy = "^1.15.0"

[tool.ruff]
ignore = [
    "E402", # module level import not at top of file
    "E722", # do not use bare except
    "F403", # 'from module import *'
    "F541", # f-string missing placeholders 
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
tradertools = "tradertools.main:main"
