# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# config
.env
db_config.yaml
connections.yaml


# VS Code
.vscode/*
!.vscode/launch.json
!.vscode/tasks.json
!.vscode/settings.json

# PyCharm
.idea/

# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/

# OS specific
.DS_Store
Thumbs.db

# local only
sandbox/
zandbox/
trash/