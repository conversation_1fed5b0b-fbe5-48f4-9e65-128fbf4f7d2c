{"version": "0.2.0", "configurations": [{"name": "Python: <PERSON><PERSON><PERSON>", "type": "debugpy", "request": "launch", "module": "${workspaceFolderBasename}", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Main", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/src/${workspaceFolderBasename}/main.py", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}"}}]}